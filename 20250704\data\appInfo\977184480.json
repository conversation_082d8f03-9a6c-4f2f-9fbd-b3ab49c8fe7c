{"ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource114/v4/70/bf/76/70bf76a0-513a-ace3-5323-34755348086e/cc59739f-73a1-4606-a102-237622bdc964_p-1.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource124/v4/60/70/51/6070511a-202b-45bf-a9c4-630c3ebd021b/63ffbd41-3138-49d8-bec8-24bc2931abe3_p-2.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource114/v4/4e/fa/47/4efa47b8-17cd-f13a-6f58-df4f506dcb16/dfd5943c-0fe4-4752-8d7b-9018d6adca2f_p-3.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource124/v4/2d/6b/e9/2d6be97e-2e40-8032-2cea-33296ebfdbe7/342127f0-ad7d-40e4-99da-5ccffcdf3b25_p-4.png/576x768bb.png"], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple126/v4/18/cf/67/18cf67de-55f7-c0e2-9796-6dce2f48872b/AppIcon-0-0-1x_U007emarketing-0-0-0-7-0-0-sRGB-0-0-0-G<PERSON>S2_U002c0-512MB-85-220-0-0.png/512x512bb.jpg", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource114/v4/a6/8a/8f/a68a8f81-b080-4a2c-1d1f-351385012370/3d62d24f-075f-452a-b0da-7895fa32a2ed_55-1.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource124/v4/c8/cb/a6/c8cba6bf-cfd1-ab86-8db1-3d6b7f99834c/c8a48451-63fe-463f-80a4-1e5e9385b3e1_55-2.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource124/v4/82/8c/a4/828ca44e-3928-9fff-0027-06a76f5e0dd4/64f10de2-a0dd-4a46-82ec-69b9ee7d412a_55-3.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource114/v4/f4/78/5c/f4785c99-4f7e-fdd8-1611-471c6aa2edda/b927c109-0ad9-4e52-941e-27d0b1835482_55-4.png/392x696bb.png"], "features": ["iosUniversal"], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": [], "isGameCenterEnabled": false, "kind": "software", "artistViewUrl": "https://apps.apple.com/us/developer/o<PERSON>-s<PERSON><PERSON><PERSON>/id947049002?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple126/v4/18/cf/67/18cf67de-55f7-c0e2-9796-6dce2f48872b/AppIcon-0-0-1x_U007emarketing-0-0-0-7-0-0-sRGB-0-0-0-G<PERSON>S2_U002c0-512MB-85-220-0-0.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple126/v4/18/cf/67/18cf67de-55f7-c0e2-9796-6dce2f48872b/AppIcon-0-0-1x_U007emarketing-0-0-0-7-0-0-sRGB-0-0-0-G<PERSON>S2_U002c0-512MB-85-220-0-0.png/100x100bb.jpg", "averageUserRatingForCurrentVersion": 4.83839, "sellerUrl": "https://offline-bibles.web.app", "languageCodesISO2A": ["EN", "ES"], "fileSizeBytes": "19213312", "formattedPrice": "Free", "userRatingCountForCurrentVersion": 4319, "trackContentRating": "4+", "artistId": 947049002, "artistName": "<PERSON><PERSON>", "genres": ["Books", "Education"], "price": 0, "trackId": 977184480, "currentVersionReleaseDate": "2022-02-22T07:12:59Z", "isVppDeviceBasedLicensingEnabled": true, "genreIds": ["6018", "6017"], "bundleId": "english.book.-611-KJV-Bible", "sellerName": "<PERSON><PERSON>", "trackName": "1611 King James <PERSON> Version", "releaseDate": "2015-03-23T23:53:22Z", "releaseNotes": "Bug fixes and performance improvements", "version": "5.2", "wrapperType": "software", "currency": "USD", "description": "This is the original 1611 King James Bible with Apocrypha\nNo internet connection needed.\nThe King James Bible is the most printed book in the history of the world. Any so-called “1611” King <PERSON> Version you buy today at the local Christian Bookstore is absolutely NOT the 1611. .. it is the 1769 Baskerville Birmingham revision, even though it admits that nowhere, and may even say “1611” in the front… it’s just not true. Prepare to be shocked! The spellings have been revised, and some words changed, in almost every printing done since 1769, and fourteen entire books plus extra prefatory features have been removed from almost every printing done since 1885!\nThe 1611 King James Bible was writen more than four hundred years ago when the English language was different. The original 1611 A.D. text, written in Early Modern English, shows the language in closer association with its Latin roots. Spelling was in Jacobean style which was not entirely standardized, but could be read phonetically. The original typeface was in Gothic style. Although both the typestyle and the older language may be considered difficult to read by some 21st Century English readers, the King James Version is recognized and respected for its beauty, cadence, and poetic feel.\nEnglish spelling differences in John 3:16\n- U = V (Example: loued = loved; gauve = gave)\n- y with 'e' above it was used as represent the 'thorn' character, which means 'the') \n- nn (<PERSON><PERSON> = Son)\nOther spelling differences\n- V = U (Example: vnto = unto | See John 1:11)\n- VV = W (Example: svvord = sword) [The V was called a 'U', this is why we still call a W a 'double U']\n- I = J (Example: Iesus = Jesus | See John 1:17)\n- Long \"s\" letters look similar to \"f\" letters (Notice the 'Old Testament' type example on the right)", "trackCensoredName": "1611 King James <PERSON> Version", "trackViewUrl": "https://apps.apple.com/us/app/1611-king-james-bible-version/id977184480?uo=4", "contentAdvisoryRating": "4+", "minimumOsVersion": "12.0", "averageUserRating": 4.83839, "primaryGenreName": "Book", "primaryGenreId": 6018, "userRatingCount": 4319}