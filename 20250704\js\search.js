/**
 * Search page specific JavaScript functionality
 */

// Page-specific variables
let searchQuery = '';
let searchResults = [];
let filteredResults = [];
let displayedResults = [];
let currentPage = 1;
let isLoading = false;
let currentSort = 'relevance';
let currentCategory = '';
let searchStartTime = 0;

/**
 * Initialize search page
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeSearchPage();
});

/**
 * Initialize search page functionality
 */
async function initializeSearchPage() {
    try {
        // Get search query from URL parameter
        searchQuery = getUrlParameter('q') || '';
        
        // Wait for common data to load
        if (!appData) {
            appData = await loadAppData();
        }
        
        if (appData) {
            initializeSearchInterface();
            initializeSorting();
            initializeFiltering();
            initializeLoadMore();
            
            if (searchQuery) {
                performSearch();
            } else {
                showPopularApps();
            }

            // Initialize lazy loading after content is loaded
            setTimeout(() => {
                initGlobalLazyLoading();
            }, 1000);
        }
    } catch (error) {
        console.error('Error initializing search page:', error);
        showError('Failed to initialize search page. Please try again.');
    }
}

/**
 * Initialize search interface
 */
function initializeSearchInterface() {
    // Set search input value
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = searchQuery;
    }
    
    // Update search query display
    updateSearchQuery();
    
    // Load categories for filter
    loadCategoryFilter();
}

/**
 * Update search query display
 */
function updateSearchQuery() {
    const searchQueryElement = document.getElementById('searchQuery');
    
    if (searchQueryElement) {
        if (searchQuery) {
            searchQueryElement.innerHTML = `Searching for: <strong>"${searchQuery}"</strong>`;
        } else {
            searchQueryElement.textContent = 'Enter a search term to find apps';
        }
    }
}

/**
 * Load category filter options
 */
function loadCategoryFilter() {
    const categoryFilter = document.getElementById('categoryFilter');
    
    if (categoryFilter && appData && appData.metadata && appData.metadata.genres) {
        const categories = appData.metadata.genres;
        
        // Clear existing options (except "All Categories")
        categoryFilter.innerHTML = '<option value="">All Categories</option>';
        
        Object.entries(categories).forEach(([id, name]) => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = name;
            categoryFilter.appendChild(option);
        });
    }
}

/**
 * Perform search
 */
function performSearch() {
    if (!searchQuery.trim()) {
        showPopularApps();
        return;
    }
    
    searchStartTime = performance.now();
    
    // Search in app data
    searchResults = searchApps(searchQuery);
    
    // Apply filters
    applyFilters();
    
    // Sort results
    sortResults(currentSort);
    
    // Reset pagination and display results
    currentPage = 1;
    displayedResults = [];
    
    if (filteredResults.length > 0) {
        hideNoResults();
        hidePopularApps();
        loadMoreResults();
    } else {
        showNoResults();
        hidePopularApps();
    }
    
    // Update search stats
    updateSearchStats();

    // Hide loading skeleton
    hideLoadingSkeleton();
}

/**
 * Apply category filter
 */
function applyFilters() {
    filteredResults = [...searchResults];
    
    // Apply category filter
    if (currentCategory) {
        filteredResults = filteredResults.filter(app => app.type === currentCategory);
    }
}

/**
 * Sort search results
 */
function sortResults(sortBy) {
    switch (sortBy) {
        case 'relevance':
            // Sort by relevance (exact matches first, then partial matches)
            filteredResults.sort((a, b) => {
                const aExact = a.appName.toLowerCase() === searchQuery.toLowerCase() ? 1 : 0;
                const bExact = b.appName.toLowerCase() === searchQuery.toLowerCase() ? 1 : 0;
                if (aExact !== bExact) return bExact - aExact;
                
                const aStarts = a.appName.toLowerCase().startsWith(searchQuery.toLowerCase()) ? 1 : 0;
                const bStarts = b.appName.toLowerCase().startsWith(searchQuery.toLowerCase()) ? 1 : 0;
                if (aStarts !== bStarts) return bStarts - aStarts;
                
                return b.rating - a.rating;
            });
            break;
        case 'rating':
            filteredResults.sort((a, b) => b.rating - a.rating);
            break;
        case 'reviews':
            filteredResults.sort((a, b) => b.num - a.num);
            break;
        case 'name':
            filteredResults.sort((a, b) => a.appName.localeCompare(b.appName));
            break;
        case 'recent':
            filteredResults.sort((a, b) => new Date(b.lastReleaseTime) - new Date(a.lastReleaseTime));
            break;
        default:
            filteredResults.sort((a, b) => b.rating - a.rating);
    }
}

/**
 * Initialize sorting functionality
 */
function initializeSorting() {
    const sortSelect = document.getElementById('sortSelect');
    
    if (sortSelect) {
        sortSelect.value = currentSort;
        sortSelect.addEventListener('change', function() {
            currentSort = this.value;
            
            if (filteredResults.length > 0) {
                sortResults(currentSort);
                
                // Reset pagination and reload
                currentPage = 1;
                displayedResults = [];
                clearResultsGrid();
                loadMoreResults();
            }
        });
    }
}

/**
 * Initialize filtering functionality
 */
function initializeFiltering() {
    const categoryFilter = document.getElementById('categoryFilter');
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            currentCategory = this.value;
            
            if (searchQuery) {
                performSearch();
            }
        });
    }
}

/**
 * Load more search results (pagination)
 */
function loadMoreResults() {
    if (isLoading || filteredResults.length === 0) {
        return;
    }
    
    isLoading = true;
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    
    if (loadMoreBtn) {
        loadMoreBtn.disabled = true;
        loadMoreBtn.textContent = 'Loading...';
    }
    
    // Simulate loading delay for better UX
    setTimeout(() => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        const newResults = filteredResults.slice(startIndex, endIndex);
        
        displayedResults = [...displayedResults, ...newResults];
        renderResults(newResults);
        
        currentPage++;
        isLoading = false;
        
        // Update load more button
        if (loadMoreBtn) {
            loadMoreBtn.disabled = false;
            
            if (displayedResults.length >= filteredResults.length) {
                loadMoreBtn.textContent = 'No More Results';
                loadMoreBtn.disabled = true;
            } else {
                loadMoreBtn.textContent = 'Load More Results';
            }
        }
    }, 300);



    setTimeout(() => {
        initGlobalLazyLoading();
    }, 800);





}

/**
 * Render search results
 */
function renderResults(results) {
    const resultsGrid = document.getElementById('resultsGrid');
    
    if (!resultsGrid) {
        return;
    }
    
    results.forEach(app => {
        const resultCard = createResultCard(app);
        resultsGrid.appendChild(resultCard);
    });
}

/**
 * Create result card element
 */
function createResultCard(app) {
    const resultCard = document.createElement('div');
    resultCard.className = 'result-card';
    resultCard.addEventListener('click', () => {
        window.location.href = `./detail.html?appId=${app.appId}`;
    });
    
    // Generate star rating
    const starsHTML = generateStarRating(app.rating);
    
    // Highlight search terms in app name
    const highlightedName = highlightSearchTerms(app.appName, searchQuery);
    
    // Format last update date
    const lastUpdate = new Date(app.lastReleaseTime);
    const formattedDate = lastUpdate.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    });
    
    resultCard.innerHTML = `
        <div class="result-header">
            <img src="./images/lazy.gif" data-src="${app.icon}" alt="${app.appName}" class="result-icon lazy-load">
            <div class="result-info">
                <h3 class="result-name">${highlightedName}</h3>
                ${app.subtitle ? `<p class="result-subtitle">${app.subtitle}</p>` : ''}
                <p class="result-publisher">${app.publisher}</p>
            </div>
        </div>
        <div class="result-meta">
            <div class="result-rating">
                <div class="rating-stars">
                    ${starsHTML}
                </div>
                <span class="rating-text">${app.rating.toFixed(1)} (${formatNumber(app.num)})</span>
            </div>
            <span class="result-category">${app.type}</span>
        </div>
    `;
    
    return resultCard;
}

/**
 * Highlight search terms in text
 */
function highlightSearchTerms(text, query) {
    if (!query) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<span class="highlight">$1</span>');
}

/**
 * Clear results grid
 */
function clearResultsGrid() {
    const resultsGrid = document.getElementById('resultsGrid');
    if (resultsGrid) {
        resultsGrid.innerHTML = '';
    }
}

/**
 * Update search statistics
 */
function updateSearchStats() {
    const resultsCount = document.getElementById('resultsCount');
    const searchTime = document.getElementById('searchTime');
    
    if (resultsCount) {
        const count = filteredResults.length;
        resultsCount.textContent = `${count} result${count !== 1 ? 's' : ''} found`;
    }
    
    if (searchTime && searchStartTime) {
        const elapsed = ((performance.now() - searchStartTime) / 1000).toFixed(3);
        searchTime.textContent = `(${elapsed} seconds)`;
    }
}

/**
 * Show no results message
 */
function showNoResults() {
    const noResults = document.getElementById('noResults');
    const loadMoreContainer = document.querySelector('.load-more-container');
    
    if (noResults) {
        noResults.style.display = 'block';
        generateSearchSuggestions();
    }
    
    if (loadMoreContainer) {
        loadMoreContainer.style.display = 'none';
    }
}

/**
 * Hide no results message
 */
function hideNoResults() {
    const noResults = document.getElementById('noResults');
    const loadMoreContainer = document.querySelector('.load-more-container');
    
    if (noResults) {
        noResults.style.display = 'none';
    }
    
    if (loadMoreContainer) {
        loadMoreContainer.style.display = 'block';
    }
}

/**
 * Generate search suggestions
 */
function generateSearchSuggestions() {
    const suggestionsList = document.getElementById('suggestionsList');
    
    if (!suggestionsList || !appData || !appData.rankInfo) {
        return;
    }
    
    // Get popular app names as suggestions
    const suggestions = appData.rankInfo
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 8)
        .map(app => app.appName.split(' ')[0]) // Get first word of app names
        .filter((name, index, arr) => arr.indexOf(name) === index) // Remove duplicates
        .slice(0, 6);
    
    suggestionsList.innerHTML = '';
    
    suggestions.forEach(suggestion => {
        const li = document.createElement('li');
        li.textContent = suggestion;
        li.addEventListener('click', () => {
            searchQuery = suggestion;
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = suggestion;
            }
            updateSearchQuery();
            performSearch();
        });
        suggestionsList.appendChild(li);
    });
}

/**
 * Show popular apps when no search query
 */
function showPopularApps() {
    const popularApps = document.getElementById('popularApps');
    const popularGrid = document.getElementById('popularGrid');
    
    if (popularApps && popularGrid && appData && appData.rankInfo) {
        // Get top rated apps
        const topApps = appData.rankInfo
            .sort((a, b) => b.rating - a.rating)
            .slice(0, 12);
        
        popularGrid.innerHTML = '';
        
        topApps.forEach(app => {
            const appCard = createResultCard(app);
            appCard.classList.add('popular-card');
            popularGrid.appendChild(appCard);
        });
        
        popularApps.style.display = 'block';
        
        // Update search stats
        const resultsCount = document.getElementById('resultsCount');
        if (resultsCount) {
            resultsCount.textContent = 'Popular apps';
        }

        // Hide loading skeleton
        hideLoadingSkeleton();
    }
}

/**
 * Hide popular apps section
 */
function hidePopularApps() {
    const popularApps = document.getElementById('popularApps');
    if (popularApps) {
        popularApps.style.display = 'none';
    }
}

/**
 * Initialize load more functionality
 */
function initializeLoadMore() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreResults);
    }
}

/**
 * Handle search form submission
 */
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('searchForm');
    
    if (searchForm) {
        searchForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            const searchInput = document.getElementById('searchInput');
            const newQuery = searchInput.value.trim();
            
            if (newQuery !== searchQuery) {
                searchQuery = newQuery;
                updateSearchQuery();
                
                if (searchQuery) {
                    // Update URL without page reload
                    const newUrl = `${window.location.pathname}?q=${encodeURIComponent(searchQuery)}`;
                    window.history.pushState({ query: searchQuery }, '', newUrl);
                    
                    performSearch();
                } else {
                    showPopularApps();
                    hideNoResults();
                }
            }
        });
    }
});

/**
 * Handle browser back/forward navigation
 */
window.addEventListener('popstate', function(event) {
    if (event.state && event.state.query !== undefined) {
        searchQuery = event.state.query;
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = searchQuery;
        }
        updateSearchQuery();

        if (searchQuery) {
            performSearch();
        } else {
            showPopularApps();
            hideNoResults();
        }
    }
});

/**
 * Search apps in the data
 */
function searchApps(query) {
    if (!appData || !appData.rankInfo || !query) {
        return [];
    }

    const searchTerm = query.toLowerCase().trim();
    const results = [];

    appData.rankInfo.forEach(app => {
        let relevanceScore = 0;

        // Check app name (highest priority)
        const appName = app.appName.toLowerCase();
        if (appName === searchTerm) {
            relevanceScore += 100; // Exact match
        } else if (appName.startsWith(searchTerm)) {
            relevanceScore += 80; // Starts with search term
        } else if (appName.includes(searchTerm)) {
            relevanceScore += 60; // Contains search term
        }

        // Check subtitle
        if (app.subtitle) {
            const subtitle = app.subtitle.toLowerCase();
            if (subtitle.includes(searchTerm)) {
                relevanceScore += 20;
            }
        }

        // Check publisher
        if (app.publisher) {
            const publisher = app.publisher.toLowerCase();
            if (publisher.includes(searchTerm)) {
                relevanceScore += 15;
            }
        }

        // Check category
        if (app.type) {
            const category = app.type.toLowerCase();
            if (category.includes(searchTerm)) {
                relevanceScore += 10;
            }
        }

        // Add to results if any match found
        if (relevanceScore > 0) {
            results.push({
                ...app,
                relevanceScore
            });
        }
    });

    // Sort by relevance score first, then by rating
    return results.sort((a, b) => {
        if (a.relevanceScore !== b.relevanceScore) {
            return b.relevanceScore - a.relevanceScore;
        }
        return b.rating - a.rating;
    });
}

/**
 * Debounced search function for real-time search
 */
let searchTimeout;
function debounceSearch(query, delay = 300) {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        if (query.trim()) {
            searchQuery = query.trim();
            updateSearchQuery();
            performSearch();
        }
    }, delay);
}

/**
 * Initialize real-time search
 */
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');

    if (searchInput) {
        // Real-time search as user types
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            if (query.length >= 2) {
                debounceSearch(query);
            } else if (query.length === 0) {
                searchQuery = '';
                updateSearchQuery();
                clearResultsGrid();
                showPopularApps();
                hideNoResults();

                // Update URL
                const newUrl = window.location.pathname;
                window.history.pushState({ query: '' }, '', newUrl);
            }
        });

        // Handle focus and blur for better UX
        searchInput.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    }
});

/**
 * Initialize keyboard shortcuts
 */
document.addEventListener('keydown', function(event) {
    // Focus search input when pressing '/' key
    if (event.key === '/' && !event.ctrlKey && !event.metaKey) {
        const searchInput = document.getElementById('searchInput');
        const activeElement = document.activeElement;

        // Only focus if not already in an input field
        if (searchInput && activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'TEXTAREA') {
            event.preventDefault();
            searchInput.focus();
            searchInput.select();
        }
    }

    // Clear search when pressing Escape
    if (event.key === 'Escape') {
        const searchInput = document.getElementById('searchInput');
        if (searchInput && document.activeElement === searchInput) {
            searchInput.blur();
        }
    }
});

/**
 * Add loading skeleton while searching
 */
function showLoadingSkeleton() {
    const resultsGrid = document.getElementById('resultsGrid');

    if (!resultsGrid) return;

    resultsGrid.innerHTML = '';

    // Create skeleton cards
    for (let i = 0; i < 6; i++) {
        const skeletonCard = document.createElement('div');
        skeletonCard.className = 'result-card loading';
        skeletonCard.innerHTML = `
            <div class="result-header">
                <div class="skeleton skeleton-icon"></div>
                <div class="result-info">
                    <div class="skeleton skeleton-line skeleton-title"></div>
                    <div class="skeleton skeleton-line skeleton-subtitle"></div>
                    <div class="skeleton skeleton-line skeleton-publisher"></div>
                </div>
            </div>
            <div class="result-meta">
                <div class="skeleton skeleton-line skeleton-rating"></div>
                <div class="skeleton skeleton-line skeleton-category"></div>
            </div>
        `;
        resultsGrid.appendChild(skeletonCard);
    }
}

/**
 * Hide loading skeleton
 */
function hideLoadingSkeleton() {
    const resultsGrid = document.getElementById('resultsGrid');
    if (!resultsGrid) return;

    // Remove skeleton cards
    const skeletonCards = resultsGrid.querySelectorAll('.loading');
    skeletonCards.forEach(card => card.remove());
}

/**
 * Enhanced search with loading states
 */
function performSearchWithLoading() {
    if (!searchQuery.trim()) {
        showPopularApps();
        return;
    }

    // Show loading skeleton
    showLoadingSkeleton();
    hideNoResults();
    hidePopularApps();

    // Simulate search delay for better UX
    setTimeout(() => {
        performSearch();
    }, 200);
}

/**
 * Update the original performSearch to use loading
 */
const originalPerformSearch = performSearch;
performSearch = function() {
    searchStartTime = performance.now();

    // Search in app data
    searchResults = searchApps(searchQuery);

    // Apply filters
    applyFilters();

    // Sort results
    sortResults(currentSort);

    // Reset pagination and display results
    currentPage = 1;
    displayedResults = [];
    clearResultsGrid();

    if (filteredResults.length > 0) {
        hideNoResults();
        hidePopularApps();
        loadMoreResults();
    } else {
        showNoResults();
        hidePopularApps();
    }

    // Update search stats
    updateSearchStats();
};
