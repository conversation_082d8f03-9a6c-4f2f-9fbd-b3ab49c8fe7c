<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Detail Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .app-description { background: #f9f9f9; padding: 15px; margin: 10px 0; border: 1px solid #ddd; }
        .related-apps { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 16px; margin: 20px 0; }
        .related-app-card { background: #f9f9f9; border: 1px solid #ddd; border-radius: 8px; padding: 16px; cursor: pointer; }
        .related-app-header { display: flex; align-items: center; gap: 12px; margin-bottom: 12px; }
        .related-app-icon { width: 48px; height: 48px; border-radius: 8px; object-fit: cover; }
        .related-app-info { flex: 1; }
        .related-app-name { font-weight: 600; margin-bottom: 4px; }
        .related-app-publisher { font-size: 14px; color: #666; }
        .related-app-rating { display: flex; align-items: center; gap: 8px; font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <h1>🔧 Direct Detail Page Test</h1>
    
    <div class="test-section">
        <h2>Test Elements</h2>
        <div id="appDescription" class="app-description">
            <p>Loading app description...</p>
        </div>
        <div id="relatedApps" class="related-apps">
            <!-- Related apps will be loaded here -->
        </div>
    </div>
    
    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults"></div>
    </div>

    <script>
        // Copy the exact functions from detail.js
        function updateElement(id, property, value) {
            const element = document.getElementById(id);
            if (element && value !== undefined && value !== null) {
                if (property === 'textContent' && value === '') {
                    element.style.display = 'none';
                } else {
                    element[property] = value;
                }
            }
        }
        
        function formatDescription(description) {
            if (!description) return 'No description available.';
            return description.replace(/\n/g, '<br>');
        }
        
        function generateStarRating(rating) {
            const stars = Math.round(rating);
            return '★'.repeat(stars) + '☆'.repeat(5 - stars);
        }
        
        function createRelatedAppCard(app) {
            const card = document.createElement('div');
            card.className = 'related-app-card';
            card.addEventListener('click', () => {
                alert(`Would navigate to: detail.html?appId=${app.appId}`);
            });
            
            const starsHTML = generateStarRating(app.rating);
            
            card.innerHTML = `
                <div class="related-app-header">
                    <img src="${app.icon}" alt="${app.appName}" class="related-app-icon">
                    <div class="related-app-info">
                        <h4 class="related-app-name">${app.appName}</h4>
                        <p class="related-app-publisher">${app.publisher}</p>
                    </div>
                </div>
                <div class="related-app-rating">
                    <div class="rating-stars">${starsHTML}</div>
                    <span>${app.rating.toFixed(1)}</span>
                </div>
            `;
            
            return card;
        }
        
        async function testDetailFunctions() {
            const results = [];
            
            try {
                // Test 1: Load basic data
                const response = await fetch('./data/typeList.json');
                const appData = await response.json();
                const currentAppId = 309735670;
                
                const basicAppInfo = appData.rankInfo.find(app => 
                    app.appId === currentAppId || 
                    app.appId.toString() === currentAppId.toString()
                );
                
                if (!basicAppInfo) {
                    results.push('<span class="error">❌ Basic app info not found</span>');
                    return;
                }
                
                results.push('<span class="success">✅ Basic app info loaded</span>');
                
                // Test 2: Load detailed data
                let detailedAppInfo = null;
                try {
                    const detailedResponse = await fetch(`./data/appInfo/${currentAppId}.json`);
                    detailedAppInfo = await detailedResponse.json();
                    results.push('<span class="success">✅ Detailed app info loaded</span>');
                } catch (error) {
                    results.push('<span class="warning">⚠️ Detailed app info not found, using basic only</span>');
                }
                
                // Test 3: Merge and test description
                const fullAppInfo = { ...basicAppInfo, ...detailedAppInfo };
                const description = fullAppInfo.description || fullAppInfo.subtitle || 'No description available for this app.';
                
                results.push(`<span class="success">✅ Description: ${description.length} characters</span>`);
                
                // Test 4: Update description element
                updateElement('appDescription', 'innerHTML', formatDescription(description));
                results.push('<span class="success">✅ Description element updated</span>');
                
                // Test 5: Test related apps
                const category = basicAppInfo.type;
                const relatedApps = appData.rankInfo
                    .filter(app => app.type === category && app.appId !== currentAppId)
                    .sort((a, b) => b.rating - a.rating)
                    .slice(0, 4);
                
                results.push(`<span class="success">✅ Found ${relatedApps.length} related apps</span>`);
                
                // Test 6: Render related apps
                const relatedAppsContainer = document.getElementById('relatedApps');
                relatedAppsContainer.innerHTML = '';
                
                relatedApps.forEach(app => {
                    const card = createRelatedAppCard(app);
                    relatedAppsContainer.appendChild(card);
                });
                
                results.push('<span class="success">✅ Related apps rendered</span>');
                
                // Test 7: Check if elements are actually updated
                const descElement = document.getElementById('appDescription');
                const relatedElement = document.getElementById('relatedApps');
                
                if (descElement.innerHTML.includes('Loading app description')) {
                    results.push('<span class="error">❌ Description element not updated</span>');
                } else {
                    results.push('<span class="success">✅ Description element successfully updated</span>');
                }
                
                if (relatedElement.children.length === 0) {
                    results.push('<span class="error">❌ Related apps not rendered</span>');
                } else {
                    results.push(`<span class="success">✅ ${relatedElement.children.length} related app cards rendered</span>`);
                }
                
            } catch (error) {
                results.push(`<span class="error">❌ Error: ${error.message}</span>`);
                console.error('Test error:', error);
            }
            
            document.getElementById('testResults').innerHTML = results.join('<br>');
        }
        
        // Run test
        testDetailFunctions();
    </script>
</body>
</html>
