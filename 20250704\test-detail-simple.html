<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Detail Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .app-info { background: #f9f9f9; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 Simple Detail Page Test</h1>
    
    <!-- Mock DOM elements that detail.js expects -->
    <div style="display: none;">
        <img id="appIcon" src="" alt="App Icon">
        <h1 id="appName">Loading...</h1>
        <p id="appSubtitle"></p>
        <p id="appPublisher"></p>
        <div id="appStars"></div>
        <span id="appRatingText"></span>
        <span id="appCategory"></span>
        <div id="appDescription"></div>
        <span id="appDeveloper"></span>
        <span id="appCategoryInfo"></span>
        <span id="appVersion"></span>
        <div id="relatedApps"></div>
    </div>
    
    <div class="test-section">
        <h2>1. URL Parameters</h2>
        <div id="urlParams"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Data Loading</h2>
        <div id="dataStatus">Testing...</div>
    </div>
    
    <div class="test-section">
        <h2>3. App Search</h2>
        <div id="appSearchStatus">Testing...</div>
    </div>
    
    <div class="test-section">
        <h2>4. App Information Display</h2>
        <div id="appDisplay"></div>
    </div>

    <script>
        // Mock functions that detail.js might need
        function generateStarRating(rating) {
            const stars = Math.round(rating);
            return '★'.repeat(stars) + '☆'.repeat(5 - stars);
        }
        
        function formatNumber(num) {
            return num ? num.toLocaleString() : '0';
        }
        
        function formatDescription(desc) {
            return desc || 'No description available';
        }
        
        function updateElement(id, property, value) {
            const element = document.getElementById(id);
            if (element && value !== undefined && value !== null) {
                element[property] = value;
            }
        }
        
        // Get URL parameters
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }
        
        // Test URL parameters
        const appIdParam = getUrlParameter('appId');
        const currentAppId = appIdParam ? parseInt(appIdParam, 10) : null;
        
        document.getElementById('urlParams').innerHTML = `
            <p><strong>appId parameter:</strong> ${appIdParam}</p>
            <p><strong>parsed appId:</strong> ${currentAppId}</p>
            <p><strong>Current URL:</strong> ${window.location.href}</p>
        `;
        
        // Test data loading and app search
        async function testDetailPage() {
            try {
                document.getElementById('dataStatus').innerHTML = '<span class="warning">Loading data...</span>';
                
                // Load data
                const response = await fetch('./data/typeList.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const appData = await response.json();
                document.getElementById('dataStatus').innerHTML = '<span class="success">✅ Data loaded successfully</span>';
                
                // Search for app
                if (currentAppId && appData.rankInfo) {
                    const foundApp = appData.rankInfo.find(app => 
                        app.appId === currentAppId || 
                        app.appId.toString() === currentAppId.toString()
                    );
                    
                    if (foundApp) {
                        document.getElementById('appSearchStatus').innerHTML = '<span class="success">✅ App found in data</span>';
                        
                        // Display app information
                        displayAppInfo(foundApp);
                        
                        // Test related apps
                        testRelatedApps(foundApp, appData);
                        
                    } else {
                        document.getElementById('appSearchStatus').innerHTML = '<span class="error">❌ App not found in data</span>';
                        
                        // Show some sample apps for debugging
                        const sampleApps = appData.rankInfo.slice(0, 5).map(app => ({
                            appId: app.appId,
                            appName: app.appName,
                            type: app.type
                        }));
                        
                        document.getElementById('appDisplay').innerHTML = `
                            <p><strong>Looking for appId:</strong> ${currentAppId}</p>
                            <p><strong>Sample apps in data:</strong></p>
                            <pre>${JSON.stringify(sampleApps, null, 2)}</pre>
                        `;
                    }
                } else {
                    document.getElementById('appSearchStatus').innerHTML = '<span class="warning">⚠️ No appId to search for</span>';
                }
                
            } catch (error) {
                document.getElementById('dataStatus').innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
                console.error('Error:', error);
            }
        }
        
        function displayAppInfo(app) {
            const starsHTML = generateStarRating(app.rating);
            
            document.getElementById('appDisplay').innerHTML = `
                <div class="app-info">
                    <h3>${app.appName}</h3>
                    <p><strong>Publisher:</strong> ${app.publisher}</p>
                    <p><strong>Category:</strong> ${app.type}</p>
                    <p><strong>Rating:</strong> ${starsHTML} ${app.rating.toFixed(1)} (${formatNumber(app.num)})</p>
                    <p><strong>Subtitle:</strong> ${app.subtitle || 'No subtitle'}</p>
                    <p><strong>Icon:</strong> <img src="${app.icon}" alt="${app.appName}" style="width: 50px; height: 50px; border-radius: 8px;"></p>
                </div>
            `;
            
            // Test DOM updates
            updateElement('appName', 'textContent', app.appName);
            updateElement('appPublisher', 'textContent', app.publisher);
            updateElement('appCategory', 'textContent', app.type);
            updateElement('appStars', 'innerHTML', starsHTML);
            updateElement('appRatingText', 'textContent', `${app.rating.toFixed(1)} (${formatNumber(app.num)})`);
            updateElement('appDescription', 'innerHTML', formatDescription(app.subtitle));
        }
        
        function testRelatedApps(currentApp, appData) {
            const relatedApps = appData.rankInfo
                .filter(app => app.type === currentApp.type && app.appId !== currentApp.appId)
                .sort((a, b) => b.rating - a.rating)
                .slice(0, 5);
            
            const relatedHtml = relatedApps.map(app => `
                <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0;">
                    <strong>${app.appName}</strong><br>
                    ${app.publisher}<br>
                    ${generateStarRating(app.rating)} ${app.rating.toFixed(1)}
                </div>
            `).join('');
            
            document.getElementById('appDisplay').innerHTML += `
                <h4>Related Apps (${relatedApps.length} found):</h4>
                ${relatedHtml}
            `;
        }
        
        // Run test
        testDetailPage();
    </script>
</body>
</html>
