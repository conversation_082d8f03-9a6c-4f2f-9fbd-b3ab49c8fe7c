<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Detail Test</title>
</head>
<body>
    <h1>Minimal Detail Test</h1>
    
    <!-- Test elements -->
    <div id="appDescription">Loading...</div>
    <div id="relatedApps"></div>
    
    <div id="console-output" style="background: #f0f0f0; padding: 20px; margin: 20px 0; white-space: pre-wrap; font-family: monospace;"></div>

    <!-- Include the same scripts as detail.html -->
    <script src="./js/common.js"></script>
    <script>
        // Override console.log to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function logToPage(type, ...args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.textContent += `[${type}] ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = (...args) => {
            originalLog(...args);
            logToPage('LOG', ...args);
        };
        
        console.error = (...args) => {
            originalError(...args);
            logToPage('ERROR', ...args);
        };
        
        // Test the detail page functions directly
        async function testDetailPageFunctions() {
            console.log('🧪 Starting minimal detail test...');
            
            try {
                // Test URL parameter parsing
                const testUrl = 'http://localhost:8081/detail.html?appId=309735670';
                const urlParams = new URLSearchParams('?appId=309735670');
                const appIdParam = urlParams.get('appId');
                const currentAppId = appIdParam ? parseInt(appIdParam, 10) : null;
                
                console.log('URL parameter test:', appIdParam, '→', currentAppId);
                
                // Test data loading
                console.log('Loading app data...');
                const appData = await loadAppData();
                
                if (!appData || !appData.rankInfo) {
                    console.error('Failed to load app data');
                    return;
                }
                
                console.log('App data loaded, total apps:', appData.rankInfo.length);
                
                // Find the specific app
                const basicAppInfo = appData.rankInfo.find(app => 
                    app.appId === currentAppId || 
                    app.appId.toString() === currentAppId.toString()
                );
                
                if (!basicAppInfo) {
                    console.error('App not found');
                    return;
                }
                
                console.log('Found app:', basicAppInfo.appName);
                
                // Test detailed data loading
                let appDetails = null;
                try {
                    appDetails = await loadData(`./data/appInfo/${currentAppId}.json`);
                    console.log('Detailed data loaded');
                } catch (error) {
                    console.log('No detailed data available');
                }
                
                // Test description
                const fullAppInfo = { ...(basicAppInfo || {}), ...(appDetails || {}) };
                const description = fullAppInfo.description || fullAppInfo.subtitle || 'No description available for this app.';
                
                console.log('Final description length:', description.length);
                
                // Update description element
                const descElement = document.getElementById('appDescription');
                if (descElement) {
                    descElement.innerHTML = formatDescription(description);
                    console.log('Description element updated');
                } else {
                    console.error('Description element not found');
                }
                
                // Test related apps
                const category = basicAppInfo.type;
                const relatedApps = appData.rankInfo
                    .filter(app => app.type === category && app.appId !== currentAppId)
                    .sort((a, b) => b.rating - a.rating)
                    .slice(0, 4);
                
                console.log('Found', relatedApps.length, 'related apps');
                
                // Create simple related apps display
                const relatedElement = document.getElementById('relatedApps');
                if (relatedElement) {
                    relatedElement.innerHTML = '<h3>Related Apps:</h3>' + 
                        relatedApps.map(app => `<p>${app.appName} (${app.rating})</p>`).join('');
                    console.log('Related apps element updated');
                } else {
                    console.error('Related apps element not found');
                }
                
                console.log('✅ Test completed successfully');
                
            } catch (error) {
                console.error('Test failed:', error);
            }
        }
        
        // Run test when page loads
        document.addEventListener('DOMContentLoaded', testDetailPageFunctions);
    </script>
</body>
</html>
