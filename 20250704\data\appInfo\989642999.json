{"isGameCenterEnabled": false, "features": ["iosUniversal"], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": [], "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/51/c5/4d/51c54d62-1a7b-641e-e510-52cc877339cd/mzl.kuxepnaf.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/02/5b/64/025b6484-7aac-5fc0-c6f2-7c8a4937c143/mzl.plyhjvyu.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/ec/34/aa/ec34aa75-4f56-73ee-6b88-55f859f9dc0a/pr_source.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/46/0f/81/460f81f8-8b14-ed46-d89d-ad7517e2659e/mzl.xwlsnnag.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/98/8d/b3/988db3e7-d233-b06d-7706-8f14dc36e428/mzl.nkzkuahg.png/392x696bb.png"], "ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/18/c1/e0/18c1e034-61a5-f17f-e5f7-a953a6158eaa/mzl.zafglduj.png/552x414bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/be/a4/32/bea432e9-b8d5-434a-194b-285cb9cb0c22/mzl.bprfsurs.png/552x414bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/cc/9b/b6/cc9bb6ec-64dd-e247-8145-e4877a5f6994/pr_source.png/552x414bb.png"], "appletvScreenshotUrls": [], "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/98/16/a5/9816a5da-e1fb-e377-70d7-680992507631/AppIcon-0-0-1x_U007emarketing-0-5-85-220.png/60x60bb.jpg", "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/98/16/a5/9816a5da-e1fb-e377-70d7-680992507631/AppIcon-0-0-1x_U007emarketing-0-5-85-220.png/512x512bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/98/16/a5/9816a5da-e1fb-e377-70d7-680992507631/AppIcon-0-0-1x_U007emarketing-0-5-85-220.png/100x100bb.jpg", "artistViewUrl": "https://apps.apple.com/us/developer/touch-foo/id331891508?uo=4", "kind": "software", "userRatingCountForCurrentVersion": 257, "minimumOsVersion": "13.0", "averageUserRatingForCurrentVersion": 4.3891, "averageUserRating": 4.3891, "trackCensoredName": "Shelly - SSH Client", "languageCodesISO2A": ["EN"], "fileSizeBytes": "10714112", "formattedPrice": "Free", "contentAdvisoryRating": "4+", "trackViewUrl": "https://apps.apple.com/us/app/shelly-ssh-client/id989642999?uo=4", "trackContentRating": "4+", "artistId": 331891508, "artistName": "Touch Foo", "genres": ["Developer Tools", "Utilities"], "price": 0, "releaseDate": "2016-06-11T10:20:06Z", "primaryGenreName": "Developer Tools", "primaryGenreId": 6026, "bundleId": "com.touchfoo.Shelly", "genreIds": ["6026", "6002"], "trackId": 989642999, "trackName": "Shelly - SSH Client", "isVppDeviceBasedLicensingEnabled": true, "sellerName": "<PERSON>", "currentVersionReleaseDate": "2025-06-24T12:10:28Z", "releaseNotes": "New in 1.4.2:\n- Fixed a UI layout issue where the terminal contents was sometimes obscured by the keyboard toolbar.", "version": "1.4.2", "wrapperType": "software", "currency": "USD", "description": "SSH client designed for usability and performance.\n\nRELIABLE AND FAST\n• Hardware accelerated text rendering.\n• Robust terminal emulation based on the PuTTY engine.\n• Works great with programs such as 'screen' and 'irssi'.\n\nCONTROL WITH GESTURES\n• Default gestures optimized for irssi. Just swipe to switch channels or to scroll up and down.\n• Fully customizable. Any keypress or action can be attached to any gesture.\n\nEASY CONNECTING\n• Save user credentials and configure a remote command such as ‘screen -rd’ to connect with one tap.\n• 3D touch support. Connect directly from the iOS home screen.\n\nCUSTOMIZABLE KEYBOARD\n• Easily bind custom actions to buttons on the keyboard toolbar.\n• Works with custom iOS keyboards such as SwiftKey.\n\nOPEN LINKS AND SELECT TEXT\n• Familiar iOS text selection experience. Long-press on text to show the selection controls.\n• Copy and paste text.\n• Select a URL to easily open it in the web browser.\n\nOTHER FEATURES\n• iCloud sync support.\n• Terminal color themes. Choose your favorite from the several themes included or make your own.\n• Key file authentication support. Import an existing private key, or create a new key and export the public key.\n• Host key verification and management.\n• Agent forwarding (OpenSSH servers only).\n• Keep connections alive while the app is in the background.\n• Connection history with optional realtime location data.\n• Plenty of other configuration options.\n\nPlease note that some features require a paid upgrade. You can try all features for free for a limited time.", "userRatingCount": 257}