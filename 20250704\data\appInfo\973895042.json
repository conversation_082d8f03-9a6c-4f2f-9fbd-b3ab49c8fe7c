{"artistViewUrl": "https://apps.apple.com/us/developer/kraus-und-karnath-gbr-2kit-consulting/id477998014?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/61/6a/a2/616aa2cf-5251-9546-6d28-be69aa401b6b/AppIcon-0-0-1x_U007epad-0-1-0-0-85-220.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/61/6a/a2/616aa2cf-5251-9546-6d28-be69aa401b6b/AppIcon-0-0-1x_U007epad-0-1-0-0-85-220.png/100x100bb.jpg", "features": ["iosUniversal"], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": ["Unrestricted Web Access"], "isGameCenterEnabled": false, "kind": "software", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/89/c1/35/89c135b3-e6f3-1a1f-5316-e6d45f494d96/d3f45f4f-e56d-4e0a-aa5a-88c3dbe47d9f_Samsung5.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/e0/34/23/e034236b-4717-87c2-962b-5b28c7c08529/3d99564a-67f9-42a6-a92e-a403fc16f140_Samsung6.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/a7/f0/2f/a7f02f64-cc7e-6fc0-6bfb-c93efc47245a/72b2b308-b207-425b-92fa-e891a8a2014f_Samsung7.png/392x696bb.png"], "ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/7b/f2/83/7bf2831a-a920-f9ae-b1b3-d9d43b8d7c89/44b9f5dd-168b-4561-9f12-2d9b94916a79_Samsung10.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/5c/18/85/5c18855c-14f4-cfea-05c8-0209ac8f5d60/8c358f62-613a-40f6-b0a6-11e280bb2337_Samsung11.png/576x768bb.png"], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/61/6a/a2/616aa2cf-5251-9546-6d28-be69aa401b6b/AppIcon-0-0-1x_U007epad-0-1-0-0-85-220.png/512x512bb.jpg", "trackCensoredName": "TV Cast for Samsung TV App", "trackViewUrl": "https://apps.apple.com/us/app/tv-cast-for-samsung-tv-app/id973895042?uo=4", "contentAdvisoryRating": "17+", "averageUserRating": 3.80524, "artistId": 477998014, "artistName": "Kraus und Karnath GbR 2Kit Consulting", "genres": ["Photo & Video", "Entertainment"], "price": 0, "bundleId": "de.2kit.cast-browser-samsung", "genreIds": ["6008", "6016"], "releaseDate": "2015-03-26T13:22:35Z", "trackName": "TV Cast for Samsung TV App", "trackId": 973895042, "isVppDeviceBasedLicensingEnabled": true, "sellerName": "Kraus und Karnath GbR 2Kit Consulting", "currentVersionReleaseDate": "2025-02-26T17:49:08Z", "releaseNotes": "- Bug fixes", "version": "3.8", "wrapperType": "software", "currency": "USD", "description": "Watch any web-video, online movie, livestream or live tv show on your Samsung Smart TV or Blu-ray Player. Enjoy the show on your big screen with the no#1 web video streamer.\n\nWith Video & TV Cast for Samsung Smart TV you can browse the web and stream any web video, online movie, livestream or live tv show you want on your Samsung Smart TV. Mp4, m3u8, hls livestreams and also video-over-https are supported.\n \nThere is no time limit at all! You can watch videos of 2 minutes or 2 hours or even more. Just open Video & TV Cast, navigate to a website, wait for link detection and send the video with a single tap to your Samsung Smart TV. The discovered link will be shown below the browser. A tap on the link will start the show. You don't need to install a big media server like Plex or any other third party software.\n\n>>> Important Notes\n\n* To enable video casting please open the Samsung App Store (Smart Hub) on your TV or Blu-ray Player, search for 'TV Cast' and install the receiver app.\n\n* Please enter the ip-adress of your iPhone/iPad by using the number pad and up/down/left/right keys on your Samsung TV Remote. Just enter the numbers, no need to press the enter key.\n\n* iTunes movies, Flash video and other DRM protected videos like HBO now are not supported!\n\n* Please test your favorite videos with the free edition before upgrading!\n\n* Please use the Remote Playbar for video control while browsing (see more info below)\n\n* The app streams only the video part of a website and not the whole website content (No screen or tab mirroring!). \n\n* Sometimes it is necessary to play the video on your iPad or iPhone first before it gets discovered by the app for streaming. It may also be neccessary to start casting multiple times until it works with specific videos.\n\n* If a specific video is not working, please check the FAQ on the app startpage. If that does not help you, please drop us an email before leaving a negative comment on the App Store. We will try to add support for that website as soon as possible.\n\n\n>>> UPGRADE FEATURES (available as in-app purchases)\n\n* Premium Edition: Unlocks all of the features listed here. Also future features are included, so you do not have to pay for them when they are released in an updated app version. \n\n* Remote Playbar: Use the Playbar for advanced video control while browsing, including video scrubbing, forward, rewind, play, pause, stop. The playbar works on all Samsung Smart TVs and Blu-ray Players. You can also use the included TV Remote for basic video control (play, pause, stop, forward and rewind videos) and navigating in Samsung Smart Hub. The TV Remote currently works on 2010 - 2013 TVs.\n\n* Local Videos: Cast your camera roll videos from an iPhone or iPad. Supports mp4, m4v and mov videos that were saved to the camera roll via Apples SD-Card/USB Adapter, iTunes Desktop Software or third party apps.\n\n* Ad-Blocker: Blocks ads & popups on most websites and removes the sponsoring ads from the app. You can update and enable/disable ad-blocking at any time in the settings. \n\n* Bookmarks: Save unlimited website bookmarks. Synchronize and transfer them between different TV Cast apps and iOS devices by using backup and restore via iCloud.\n\n* Desktop Mode: Change the browser user-agent and cloak your iPhone/iPad as a desktop PC to load the desktop website instead of a mobile website. Please note that this will NOT enable video casting on websites that serve Flash videos in desktop mode.\n\n* Change Homepage: Set your personal homepage in the settings.\n\n\nDisclaimer: This app is not affiliated with Samsung or any other trademark mentioned here.", "averageUserRatingForCurrentVersion": 3.80524, "minimumOsVersion": "12.0", "sellerUrl": "https://video-tv-cast.com/samsung-smart-tv", "languageCodesISO2A": ["EN"], "fileSizeBytes": "36422656", "formattedPrice": "Free", "userRatingCountForCurrentVersion": 28923, "trackContentRating": "17+", "primaryGenreName": "Photo & Video", "primaryGenreId": 6008, "userRatingCount": 28923}