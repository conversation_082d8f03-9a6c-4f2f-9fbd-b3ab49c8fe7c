/**
 * Common JavaScript functionality for all pages
 */

// Global variables
let appData = null;
let categoriesData = null;

/**
 * Global lazy loading functionality for images
 */
function initGlobalLazyLoading() {
    const lazyImages = document.querySelectorAll('.lazy-load');
    console.log('Found', lazyImages.length, 'lazy images to load');

    // First, ensure all images have correct placeholder
    lazyImages.forEach((img, index) => {
        const currentPath = window.location.pathname;
        let lazyGifPath = './images/lazy.gif';

        if (currentPath.includes('/details/')) {
            lazyGifPath = '../images/lazy.gif';
        } else if (currentPath.includes('/types/')) {
            lazyGifPath = '../images/lazy.gif';
        }

        // Set placeholder if not already set correctly
        if (!img.src || img.src === '' || !img.src.includes('lazy.gif')) {
            img.src = lazyGifPath;
            // console.log(`Set placeholder for image ${index + 1}:`, lazyGifPath);
        }
    });

    // Then load actual images
    lazyImages.forEach((img, index) => {
        const actualSrc = img.dataset.src;
        // console.log(`Loading image ${index + 1}:`, actualSrc);

        if (actualSrc) {
            // Create a new image to preload
            const tempImg = new Image();
            tempImg.onload = function() {
                // console.log(`Image ${index + 1} loaded successfully:`, actualSrc);
                img.src = actualSrc;
                img.classList.remove('lazy-load');
                img.classList.add('lazy-loaded');
                img.style.backgroundImage = 'none';
            };
            tempImg.onerror = function() {
                console.warn(`Failed to load image ${index + 1}:`, actualSrc);
                img.classList.add('lazy-error');

                // Try fallback to local placeholder
                const currentPath = window.location.pathname;
                let fallbackPath = './images/app-placeholder.svg';

                if (currentPath.includes('/details/')) {
                    fallbackPath = '../images/app-placeholder.svg';
                } else if (currentPath.includes('/types/')) {
                    fallbackPath = '../images/app-placeholder.svg';
                }

                img.src = fallbackPath;
                console.log(`Using fallback image for ${index + 1}:`, fallbackPath);
            };
            tempImg.src = actualSrc;
        }
    });
}

// Constants
const ITEMS_PER_PAGE = 30;

// Dynamic API base URL based on current page location
function getApiBaseUrl() {
    const currentPath = window.location.pathname;
    if (currentPath.includes('/types/') || currentPath.includes('/details/')) {
        return '../data';
    }
    return './data';
}

const API_BASE_URL = getApiBaseUrl();

/**
 * Initialize common functionality
 */
document.addEventListener('DOMContentLoaded', async function() {
    // Load app data first, then initialize features that depend on it
    await loadAppData();
    initializeCommonFeatures();
});

/**
 * Initialize common features like navigation, search, back to top button
 */
function initializeCommonFeatures() {
    initializeNavigation();
    initializeSearch();
    initializeBackToTop();
    initializeMobileMenu();
}

/**
 * Initialize navigation functionality
 */
function initializeNavigation() {
    // Load categories for navigation dropdown
    loadCategories();
    
    // Set active navigation link based on current page
    setActiveNavLink();
}

/**
 * Initialize search functionality
 */
function initializeSearch() {
    const searchForms = document.querySelectorAll('.search-form');
    
    searchForms.forEach(form => {
        form.addEventListener('submit', handleSearch);
    });
}

/**
 * Handle search form submission
 */
function handleSearch(event) {
    event.preventDefault();

    const searchInput = event.target.querySelector('.search-input');
    const query = searchInput.value.trim();

    if (query) {
        // Determine correct path to search page based on current location
        const currentPath = window.location.pathname;
        let searchPageUrl;

        if (currentPath.includes('/types/') || currentPath.includes('/details/')) {
            searchPageUrl = `../search.html?q=${encodeURIComponent(query)}`;
        } else {
            searchPageUrl = `search.html?q=${encodeURIComponent(query)}`;
        }

        window.location.href = searchPageUrl;
    }
}

/**
 * Initialize back to top button
 */
function initializeBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');
    
    if (backToTopBtn) {
        // Show/hide button based on scroll position
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });
        
        // Scroll to top when clicked
        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

/**
 * Initialize mobile menu functionality
 */
function initializeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const nav = document.querySelector('.nav');
    
    if (mobileMenuToggle && nav) {
        mobileMenuToggle.addEventListener('click', function() {
            nav.classList.toggle('mobile-open');
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!nav.contains(event.target) && !mobileMenuToggle.contains(event.target)) {
                nav.classList.remove('mobile-open');
            }
        });
    }
}

/**
 * Load app data from JSON file
 */
async function loadAppData() {
    try {
        showLoading();
        const response = await fetch(`${API_BASE_URL}/typeList.json`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        appData = await response.json();
        hideLoading();

        // Load categories after app data is loaded
        loadCategories();

        return appData;
    } catch (error) {
        console.error('Error loading app data:', error);
        hideLoading();
        showError('Failed to load app data. Please try again later.');
        return null;
    }
}

/**
 * Load data from any JSON file
 */
async function loadData(url) {
    try {
        const response = await fetch(url);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error loading data from', url, ':', error);
        throw error;
    }
}

/**
 * Load categories for navigation
 */
async function loadCategories() {
    try {
        if (!appData) {
            appData = await loadAppData();
        }

        if (appData) {
            // First try to use metadata.genres if available
            if (appData.metadata && appData.metadata.genres) {
                categoriesData = appData.metadata.genres;
            } else if (appData.rankInfo) {
                // Fallback: Extract unique categories from app data
                const uniqueCategories = [...new Set(appData.rankInfo.map(app => app.type))];

                // Create categories data object
                categoriesData = {};
                uniqueCategories.forEach((category, index) => {
                    if (category) {
                        categoriesData[index] = category;
                    }
                });
            }

            if (categoriesData) {
                populateNavigationCategories();
                populateFooterCategories();
            }
        }
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

/**
 * Populate navigation dropdown with categories
 */
function populateNavigationCategories() {
    const dropdown = document.getElementById('categoriesDropdown');

    if (dropdown && categoriesData) {
        dropdown.innerHTML = '';

        // Determine correct path based on current location
        const currentPath = window.location.pathname;
        const isInSubDirectory = currentPath.includes('/types/') || currentPath.includes('/details/');

        Object.entries(categoriesData).forEach(([id, name]) => {
            const li = document.createElement('li');
            const a = document.createElement('a');

            if (isInSubDirectory) {
                if(currentPath.includes('/details/')){
                    a.href = `./type.html?category=${encodeURIComponent(name)}`;
                }else{
                    a.href = `type.html?category=${encodeURIComponent(name)}`;
                }
            } else {
                a.href = `./type.html?category=${encodeURIComponent(name)}`;
            }

            a.textContent = name;
            li.appendChild(a);
            dropdown.appendChild(li);
        });
    }
}

/**
 * Populate footer with hot categories
 */
function populateFooterCategories() {
    const hotCategories = document.getElementById('hotCategories');

    if (hotCategories && categoriesData) {
        hotCategories.innerHTML = '';

        // Show first 6 categories as "hot" categories
        const hotCategoryList = Object.entries(categoriesData).slice(0, 6);

        // Determine correct path based on current location
        const currentPath = window.location.pathname;
        const isInSubDirectory = currentPath.includes('/types/') || currentPath.includes('/details/');

        hotCategoryList.forEach(([id, name]) => {
            const li = document.createElement('li');
            const a = document.createElement('a');

            if (isInSubDirectory) {
                a.href = `./type.html?category=${encodeURIComponent(name)}`;
            } else {
                a.href = `./type.html?category=${encodeURIComponent(name)}`;
            }

            a.textContent = name;
            li.appendChild(a);
            hotCategories.appendChild(li);
        });
    }
}

/**
 * Set active navigation link based on current page
 */
function setActiveNavLink() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        
        const linkHref = link.getAttribute('href');
        if (linkHref === currentPage || 
            (currentPage === '' && linkHref === 'index.html') ||
            (currentPage === 'index.html' && linkHref === 'index.html')) {
            link.classList.add('active');
        }
    });
}

/**
 * Get apps by category
 */
function getAppsByCategory(categoryName) {
    if (!appData || !appData.rankInfo) {
        return [];
    }

    // Decode URL-encoded category name and normalize
    const decodedCategory = decodeURIComponent(categoryName);

    // First try exact match
    let apps = appData.rankInfo.filter(app =>
        app.type && app.type === decodedCategory
    );

    // If no exact match, try case-insensitive match
    if (apps.length === 0) {
        apps = appData.rankInfo.filter(app =>
            app.type && app.type.toLowerCase() === decodedCategory.toLowerCase()
        );
    }

    // If still no match, try partial match
    if (apps.length === 0) {
        apps = appData.rankInfo.filter(app =>
            app.type && app.type.toLowerCase().includes(decodedCategory.toLowerCase())
        );
    }

    return apps;
}

/**
 * Search apps by name
 */
function searchApps(query) {
    if (!appData || !appData.rankInfo) {
        return [];
    }
    
    const searchTerm = query.toLowerCase();
    return appData.rankInfo.filter(app => 
        app.appName.toLowerCase().includes(searchTerm)
    );
}

/**
 * Get app details by ID
 */
async function getAppDetails(appId) {
    try {
        showLoading();
        const response = await fetch(`${API_BASE_URL}/appInfo/${appId}.json`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const appDetails = await response.json();
        hideLoading();
        
        return appDetails;
    } catch (error) {
        console.error('Error loading app details:', error);
        hideLoading();
        showError('Failed to load app details. Please try again later.');
        return null;
    }
}

/**
 * Format number with commas
 */
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

/**
 * Format description text
 */
function formatDescription(description) {
    if (!description) {
        return 'No description available.';
    }

    // Convert newlines to HTML breaks
    return description.replace(/\n/g, '<br>');
}

/**
 * Generate star rating HTML
 */
function generateStarRating(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    
    let starsHTML = '';
    
    // Full stars
    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<svg class="star" viewBox="0 0 24 24"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>';
    }
    
    // Half star
    if (hasHalfStar) {
        starsHTML += '<svg class="star half" viewBox="0 0 24 24"><defs><linearGradient id="half"><stop offset="50%" stop-color="currentColor"/><stop offset="50%" stop-color="var(--text-tertiary)"/></linearGradient></defs><path fill="url(#half)" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>';
    }
    
    // Empty stars
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<svg class="star empty" viewBox="0 0 24 24"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>';
    }
    
    return starsHTML;
}

/**
 * Show loading spinner
 */
function showLoading() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.classList.add('visible');
    }
}

/**
 * Hide loading spinner
 */
function hideLoading() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.classList.remove('visible');
    }
}

/**
 * Show error message
 */
function showError(message) {
    // Create error notification
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-notification';
    errorDiv.innerHTML = `
        <div class="error-content">
            <span class="error-message">${message}</span>
            <button class="error-close">&times;</button>
        </div>
    `;
    
    // Add styles
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--error-color);
        color: white;
        padding: 1rem;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        max-width: 400px;
    `;
    
    document.body.appendChild(errorDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
    
    // Close button functionality
    const closeBtn = errorDiv.querySelector('.error-close');
    closeBtn.addEventListener('click', () => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    });
}

/**
 * Get URL parameters
 */
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

/**
 * Debounce function for search
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
