/* Search page specific styles */

/* Search Header */
.search-header {
    background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-tertiary) 100%);
    padding: var(--spacing-2xl) 0;
    border-bottom: 1px solid var(--border-color);
}

.search-header-content {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.search-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.search-query {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    font-style: italic;
}

.search-query strong {
    color: var(--primary-color);
    font-weight: 600;
}

.search-stats {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-lg);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.results-count {
    font-weight: 600;
    color: var(--primary-color);
}

.search-time {
    opacity: 0.8;
}

/* Search Options */
.search-options {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.sort-container,
.filter-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sort-label,
.filter-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
    white-space: nowrap;
}

.sort-select,
.category-filter {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--background-primary);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 150px;
}

.sort-select:focus,
.category-filter:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.sort-select:hover,
.category-filter:hover {
    border-color: var(--primary-color);
}

/* Search Results */
.search-results {
    padding: var(--spacing-2xl) 0;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* Result Card Styles */
.result-card {
    background-color: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.result-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.1), transparent);
    transition: left 0.5s;
}

.result-card:hover::before {
    left: 100%;
}

.result-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.result-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.result-icon {
    width: 72px;
    height: 72px;
    border-radius: var(--radius-lg);
    object-fit: cover;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
}

.result-info {
    flex: 1;
    min-width: 0;
}

.result-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.result-name .highlight {
    background-color: var(--warning-color);
    color: white;
    padding: 0 2px;
    border-radius: 2px;
    font-weight: 700;
}

.result-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.result-publisher {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.result-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.result-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.star {
    width: 14px;
    height: 14px;
    fill: var(--warning-color);
}

.star.empty {
    fill: var(--text-tertiary);
}

.rating-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-left: var(--spacing-xs);
    font-weight: 500;
}

.result-category {
    background-color: var(--background-secondary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin-top: var(--spacing-xl);
}

.load-more-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-2xl);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    min-width: 160px;
}

.load-more-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.load-more-btn:disabled {
    background-color: var(--text-tertiary);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* No Results */
.no-results {
    text-align: center;
    padding: var(--spacing-2xl) 0;
}

.no-results-content {
    max-width: 500px;
    margin: 0 auto;
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.no-results-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.no-results-text {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.search-suggestions {
    background-color: var(--background-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-xl);
    text-align: left;
}

.suggestions-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.suggestions-list {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.suggestions-list li {
    background-color: var(--background-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    border: 1px solid var(--border-color);
}

.suggestions-list li:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.back-home-btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    transition: all var(--transition-fast);
}

.back-home-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Popular Apps Section */
.popular-apps {
    padding: var(--spacing-xl) 0;
    background-color: var(--background-secondary);
    border-radius: var(--radius-xl);
    margin-top: var(--spacing-xl);
}

.section-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.popular-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

/* Loading States */
.result-card.loading {
    pointer-events: none;
    opacity: 0.7;
}

.skeleton {
    background: linear-gradient(90deg, var(--background-secondary) 25%, var(--background-tertiary) 50%, var(--background-secondary) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-icon {
    width: 72px;
    height: 72px;
    border-radius: var(--radius-lg);
}

.skeleton-line {
    height: 1rem;
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-xs);
}

.skeleton-title {
    width: 80%;
}

.skeleton-subtitle {
    width: 60%;
}

.skeleton-publisher {
    width: 50%;
}

.skeleton-rating {
    width: 40%;
}

.skeleton-category {
    width: 30%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-header {
        padding: var(--spacing-xl) 0;
    }

    .search-title {
        font-size: var(--font-size-3xl);
    }

    .search-query {
        font-size: var(--font-size-lg);
    }

    .search-stats {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .search-options {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .sort-container,
    .filter-container {
        justify-content: center;
    }

    .sort-select,
    .category-filter {
        min-width: 200px;
    }

    .results-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .result-card {
        padding: var(--spacing-md);
    }

    .result-header {
        gap: var(--spacing-sm);
    }

    .result-icon {
        width: 60px;
        height: 60px;
    }

    .result-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .popular-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .suggestions-list {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .search-title {
        font-size: var(--font-size-2xl);
    }

    .search-query {
        font-size: var(--font-size-base);
    }

    .sort-select,
    .category-filter {
        min-width: 150px;
        font-size: var(--font-size-xs);
    }

    .result-icon {
        width: 56px;
        height: 56px;
    }

    .no-results-content {
        padding: 0 var(--spacing-md);
    }

    .search-suggestions {
        padding: var(--spacing-md);
    }

    .suggestions-list {
        flex-direction: column;
        align-items: center;
    }

    .suggestions-list li {
        width: 100%;
        text-align: center;
    }
}
