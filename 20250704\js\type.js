/**
 * Category page specific JavaScript functionality
 */

// Page-specific variables
let currentCategory = '';
let categoryApps = [];
let displayedApps = [];
let currentPage = 1;
let isLoading = false;
let currentSort = 'rating';

// Constants
const ITEMS_PER_PAGE = 20;

/**
 * Initialize category page
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeCategoryPage();
});

/**
 * Initialize category page functionality
 */
async function initializeCategoryPage() {
    try {
        // Get category from URL parameter
        currentCategory = getUrlParameter('category');
        
        if (!currentCategory) {
            showError('No category specified. Redirecting to homepage...');
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 2000);
            return;
        }
        
        // Wait for common data to load
        if (!appData) {
            appData = await loadAppData();
        }
        
        if (appData) {
            loadCategoryData();
            initializeSorting();
            initializeLoadMore();
            initializeSearch(); // Add search functionality

            // Initialize lazy loading after content is loaded
            setTimeout(() => {
                initGlobalLazyLoading();
            }, 500);
        }
    } catch (error) {
        console.error('Error initializing category page:', error);
        showError('Failed to initialize category page. Please try again.');
    }
}

/**
 * Load category data and apps
 */
function loadCategoryData() {
    // Update page title and meta
    document.title = `${currentCategory} - AppStore Recommendations`;
    
    // Update category header
    updateCategoryHeader();
    
    // Get apps for this category
    categoryApps = getAppsByCategory(currentCategory);
    
    if (categoryApps.length === 0) {
        showNoResults();
        return;
    }
    
    // Sort apps initially
    sortApps(currentSort);
    
    // Reset pagination and load first page
    currentPage = 1;
    displayedApps = [];
    loadMoreApps();
}

/**
 * Update category header information
 */
function updateCategoryHeader() {
    const categoryTitle = document.getElementById('categoryTitle');
    const categoryDescription = document.getElementById('categoryDescription');
    const categoryIcon = document.getElementById('categoryIcon');
    const appCount = document.getElementById('appCount');
    const lastUpdated = document.getElementById('lastUpdated');
    
    // Category icons mapping
    const categoryIcons = {
        'Business': '💼',
        'Weather': '🌤️',
        'Utilities': '🔧',
        'Travel': '✈️',
        'Sports': '⚽',
        'Social Networking': '👥',
        'Reference': '📚',
        'Productivity': '📊',
        'Photo & Video': '📸',
        'Navigation': '🗺️',
        'Music': '🎵',
        'Lifestyle': '🌟',
        'Health & Fitness': '💪',
        'Finance': '💰',
        'Entertainment': '🎬',
        'Education': '🎓',
        'Books': '📖',
        'Medical': '⚕️',
        'Magazines & Newspapers': '📰',
        'Food & Drink': '🍽️',
        'Shopping': '🛍️',
        'Developer Tools': '⚙️',
        'Graphics & Design': '🎨',
        'Kids': '👶'
    };
    
    // Category descriptions
    const categoryDescriptions = {
        'Business': 'Professional tools and productivity apps for business success',
        'Weather': 'Stay informed with accurate weather forecasts and conditions',
        'Utilities': 'Essential tools and utilities to enhance your device experience',
        'Travel': 'Plan your journeys and explore the world with travel apps',
        'Sports': 'Follow your favorite teams and stay active with sports apps',
        'Social Networking': 'Connect with friends and share your experiences',
        'Reference': 'Access information and references at your fingertips',
        'Productivity': 'Boost your efficiency with powerful productivity tools',
        'Photo & Video': 'Capture, edit, and share your visual memories',
        'Navigation': 'Find your way with GPS and mapping applications',
        'Music': 'Discover, stream, and create music with audio apps',
        'Lifestyle': 'Enhance your daily life with lifestyle and wellness apps',
        'Health & Fitness': 'Track your health and achieve your fitness goals',
        'Finance': 'Manage your money and investments with financial tools',
        'Entertainment': 'Enjoy movies, shows, and entertainment content',
        'Education': 'Learn new skills and expand your knowledge',
        'Books': 'Read and discover your next favorite book',
        'Medical': 'Healthcare tools and medical reference applications',
        'Magazines & Newspapers': 'Stay informed with news and magazine content',
        'Food & Drink': 'Discover recipes and food delivery options',
        'Shopping': 'Shop online and find the best deals',
        'Developer Tools': 'Tools and resources for developers and creators',
        'Graphics & Design': 'Create and edit visual content with design tools',
        'Kids': 'Educational and entertaining apps designed for children'
    };
    
    if (categoryTitle) {
        categoryTitle.textContent = currentCategory;
    }
    
    if (categoryDescription) {
        categoryDescription.textContent = categoryDescriptions[currentCategory] || 'Discover amazing apps in this category';
    }
    
    if (categoryIcon) {
        categoryIcon.textContent = categoryIcons[currentCategory] || '📱';
    }
    
    if (appCount) {
        const count = getAppsByCategory(currentCategory).length;
        appCount.textContent = `${count} app${count !== 1 ? 's' : ''}`;
    }
    
    if (lastUpdated) {
        lastUpdated.textContent = 'Updated recently';
    }
}

/**
 * Initialize sorting functionality
 */
function initializeSorting() {
    const sortSelect = document.getElementById('sortSelect');
    
    if (sortSelect) {
        sortSelect.value = currentSort;
        sortSelect.addEventListener('change', function() {
            currentSort = this.value;
            sortApps(currentSort);
            
            // Reset pagination and reload
            currentPage = 1;
            displayedApps = [];
            clearAppsGrid();
            loadMoreApps();
        });
    }
}

/**
 * Sort apps based on selected criteria
 */
function sortApps(sortBy) {
    switch (sortBy) {
        case 'rating':
            categoryApps.sort((a, b) => b.rating - a.rating);
            break;
        case 'reviews':
            categoryApps.sort((a, b) => b.num - a.num);
            break;
        case 'name':
            categoryApps.sort((a, b) => a.appName.localeCompare(b.appName));
            break;
        case 'recent':
            categoryApps.sort((a, b) => new Date(b.lastReleaseTime) - new Date(a.lastReleaseTime));
            break;
        default:
            categoryApps.sort((a, b) => b.rating - a.rating);
    }
}

/**
 * Load more apps (pagination)
 */
function loadMoreApps() {
    if (isLoading || categoryApps.length === 0) {
        return;
    }
    
    isLoading = true;
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    
    if (loadMoreBtn) {
        loadMoreBtn.disabled = true;
        loadMoreBtn.textContent = 'Loading...';
    }
    
    // Simulate loading delay for better UX
    setTimeout(() => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        const newApps = categoryApps.slice(startIndex, endIndex);
        
        displayedApps = [...displayedApps, ...newApps];
        renderApps(newApps);
        
        currentPage++;
        isLoading = false;
        
        // Update load more button
        if (loadMoreBtn) {
            loadMoreBtn.disabled = false;
            
            if (displayedApps.length >= categoryApps.length) {
                loadMoreBtn.textContent = 'No More Apps';
                loadMoreBtn.disabled = true;
            } else {
                loadMoreBtn.textContent = 'Load More Apps';
            }
        }
    }, 300);

    setTimeout(() => {
        initGlobalLazyLoading();
    }, 800);
}

/**
 * Render apps to the grid
 */
function renderApps(apps) {
    const appsGrid = document.getElementById('appsGrid');
    
    if (!appsGrid) {
        return;
    }
    
    apps.forEach(app => {
        const appCard = createAppCard(app);
        appsGrid.appendChild(appCard);
    });
}

/**
 * Create app card element
 */
function createAppCard(app) {
    const appCard = document.createElement('div');
    appCard.className = 'app-card';
    appCard.addEventListener('click', () => {
        window.location.href = `./detail.html?appId=${app.appId}`;
    });
    
    // Generate star rating
    const starsHTML = generateStarRating(app.rating);
    
    // Format last update date
    const lastUpdate = new Date(app.lastReleaseTime);
    const formattedDate = lastUpdate.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    });
    
    appCard.innerHTML = `
        <div class="app-header">
            <img src="../images/lazy.gif" data-src="${app.icon}" alt="${app.appName}" class="app-icon lazy-load">
            <div class="app-info">
                <h3 class="app-name">${app.appName}</h3>
                ${app.subtitle ? `<p class="app-subtitle">${app.subtitle}</p>` : ''}
                <p class="app-publisher">${app.publisher}</p>
            </div>
        </div>
        <div class="app-meta">
            <div class="app-rating">
                <div class="rating-stars">
                    ${starsHTML}
                </div>
                <span class="rating-text">${app.rating.toFixed(1)} (${formatNumber(app.num)})</span>
            </div>
            <span class="app-category">${app.type}</span>
        </div>
        <div class="app-updated">Updated ${formattedDate}</div>
    `;
    
    return appCard;
}

/**
 * Clear apps grid
 */
function clearAppsGrid() {
    const appsGrid = document.getElementById('appsGrid');
    if (appsGrid) {
        appsGrid.innerHTML = '';
    }
}

/**
 * Show no results message
 */
function showNoResults() {
    const noResults = document.getElementById('noResults');
    const loadMoreContainer = document.querySelector('.load-more-container');
    
    if (noResults) {
        noResults.style.display = 'block';
    }
    
    if (loadMoreContainer) {
        loadMoreContainer.style.display = 'none';
    }
}

/**
 * Initialize load more functionality
 */
function initializeLoadMore() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreApps);
    }
}

/**
 * Handle breadcrumb navigation
 */
function updateBreadcrumb() {
    // This could be implemented if breadcrumb navigation is added
    console.log(`Current category: ${currentCategory}`);
}

/**
 * Add loading animation
 */
function addLoadingAnimation() {
    const appsGrid = document.getElementById('appsGrid');
    
    if (appsGrid) {
        // Add skeleton loading cards
        for (let i = 0; i < 8; i++) {
            const skeletonCard = document.createElement('div');
            skeletonCard.className = 'app-card skeleton';
            skeletonCard.innerHTML = `
                <div class="app-header">
                    <div class="skeleton skeleton-icon"></div>
                    <div class="app-info">
                        <div class="skeleton skeleton-line skeleton-title"></div>
                        <div class="skeleton skeleton-line skeleton-subtitle"></div>
                        <div class="skeleton skeleton-line skeleton-publisher"></div>
                    </div>
                </div>
                <div class="app-meta">
                    <div class="skeleton skeleton-line skeleton-rating"></div>
                    <div class="skeleton skeleton-line skeleton-category"></div>
                </div>
            `;
            appsGrid.appendChild(skeletonCard);
        }
    }
}

/**
 * Remove loading animation
 */
function removeLoadingAnimation() {
    const skeletonCards = document.querySelectorAll('.app-card.skeleton');
    skeletonCards.forEach(card => card.remove());
}

/**
 * Initialize search functionality
 */
function initializeSearch() {
    const searchForm = document.getElementById('searchForm');
    const searchInput = document.getElementById('searchInput');

    if (searchForm && searchInput) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const query = searchInput.value.trim();
            if (query) {
                // Redirect to search page with query
                window.location.href = '../search.html?q=' + encodeURIComponent(query);
            }
        });

        // Handle Enter key in search input
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const query = searchInput.value.trim();
                if (query) {
                    window.location.href = '../search.html?q=' + encodeURIComponent(query);
                }
            }
        });
    }
}
