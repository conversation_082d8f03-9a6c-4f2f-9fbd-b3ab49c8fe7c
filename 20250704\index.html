<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppStore</title>
    <link rel="stylesheet" href="css/index.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/category-layouts.css">
    <link rel="stylesheet" href="./css/base.css">
    <style>
        :root {
            /* 新的色彩系统 - 更现代化的配色方案 */
            --primary-color: #0066FF;
            --secondary-color: #6C5CE7;
            --accent-color: #00D2D3;
            --success-color: #00B894;
            --warning-color: #FDCB6E;
            --error-color: #FF7675;

            /* 中性色调 */
            --text-primary: #2D3436;
            --text-secondary: #636E72;
            --text-tertiary: #B2BEC3;
            --background-primary: #FFFFFF;
            --background-secondary: #F7F9FC;
            --background-tertiary: #EFF2F7;
            --border-color: #DFE6E9;

            /* 圆角和间距 */
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-2xl: 24px;

            /* 阴影效果 */
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
            --shadow-md: 0 4px 8px rgba(0,0,0,0.08);
            --shadow-lg: 0 8px 16px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="index.html">
                        <img src="images/logo.png" alt="AppStore Recommendations Logo">
                    </a>
                </div>

                <!-- Navigation -->
                <nav class="nav">
                    <ul class="dropdown-menu" id="categoriesDropdown">
                        <!-- Categories will be loaded dynamically -->
                    </ul>
                </nav>

                <!-- Search Bar -->
                <div class="search-container">
                    <form class="search-form" id="searchForm">
                        <input type="text" class="search-input" id="searchInput" placeholder="Search apps...">
                        <button type="submit" class="search-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                        </button>
                    </form>
                </div>

                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Hero Section -->
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Discover Amazing Apps</h1>
                    <p class="hero-subtitle">
                        Your trusted source for finding the best apps from the Apple App Store. 
                        Explore curated collections, read authentic reviews, and discover your next favorite app.
                    </p>
                    <div class="hero-actions">
                        <a href="#featured" class="cta-button primary">
                            Explore Categories
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="btn-icon">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 8h6m0 0L8 14m4-6L8 2"/>
                            </svg>
                        </a>
                        <a href="search.html" class="cta-button secondary">
                            Search Apps
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="btn-icon">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.742 10.344a6.5 6.5 0 10-1.397 1.398h-.001M2 4a6 6 0 1112 0 6 6 0 01-12 0z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number" id="totalApps">0</span>
                        <span class="stat-label">Apps</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="totalCategories">0</span>
                        <span class="stat-label">Categories</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4.5+</span>
                        <span class="stat-label">Average Rating</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Category Collections Section -->
        <section class="category-collections" id="featured">
            <div class="container">

                <!-- Education Classroom Section -->
                <div class="category-row classroom-row">
                    <div class="category-collection education-collection classroom-collection">
                        <div class="collection-header">
                            <h3 class="collection-title">
                                <span class="collection-icon">📚</span>
                                Education Classroom
                            </h3>
                            <a href="./type.html?category=Education" class="view-more-link">View All</a>
                        </div>
                        <div class="collection-apps layout-classroom" id="educationApps">
                            <!-- Education apps will be loaded dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Books Library Section -->
                <div class="category-row library-row">
                    <div class="category-collection books-collection library-collection">
                        <div class="collection-header">
                            <h3 class="collection-title">
                                <span class="collection-icon">📖</span>
                                Books Library
                            </h3>
                            <a href="./type.html?category=Books" class="view-more-link">View All</a>
                        </div>
                        <div class="collection-apps layout-library" id="booksApps">
                            <!-- Books apps will be loaded dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Reference Encyclopedia Section -->
                <div class="category-row encyclopedia-row">
                    <div class="category-collection reference-collection encyclopedia-collection">
                        <div class="collection-header">
                            <h3 class="collection-title">
                                <span class="collection-icon">📚</span>
                                Reference Encyclopedia
                            </h3>
                            <a href="./type.html?category=Reference" class="view-more-link">View All</a>
                        </div>
                        <div class="collection-apps layout-encyclopedia" id="referenceApps">
                            <!-- Reference apps will be loaded dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Medical Clinic Section -->
                <div class="category-row clinic-row">
                    <div class="category-collection medical-collection clinic-collection">
                        <div class="collection-header">
                            <h3 class="collection-title">
                                <span class="collection-icon">🏥</span>
                                Medical Clinic
                            </h3>
                            <a href="./type.html?category=Medical" class="view-more-link">View All</a>
                        </div>
                        <div class="collection-apps layout-clinic" id="medicalApps">
                            <!-- Medical apps will be loaded dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Travel Journey Section -->
                <div class="category-row journey-row">
                    <div class="category-collection travel-collection journey-collection">
                        <div class="collection-header">
                            <h3 class="collection-title">
                                <span class="collection-icon">✈️</span>
                                Travel Journey
                            </h3>
                            <a href="./type.html?category=Travel" class="view-more-link">View All</a>
                        </div>
                        <div class="collection-apps layout-journey" id="travelApps">
                            <!-- Travel apps will be loaded dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Sports Scoreboard Section -->
                <div class="category-row scoreboard-row">
                    <div class="category-collection sports-collection scoreboard-collection">
                        <div class="collection-header">
                            <h3 class="collection-title">
                                <span class="collection-icon">⚽</span>
                                Sports Scoreboard
                            </h3>
                            <a href="./type.html?category=Sports" class="view-more-link">View All</a>
                        </div>
                        <div class="collection-apps layout-scoreboard" id="sportsApps">
                            <!-- Sports apps will be loaded dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Kids Playground Section -->
                <div class="category-row playground-row">
                    <div class="category-collection games-collection playground-collection">
                        <div class="collection-header">
                            <h3 class="collection-title">
                                <span class="collection-icon">🎮</span>
                                Kids Playground
                            </h3>
                            <a href="./type.html?category=Kids" class="view-more-link">View All</a>
                        </div>
                        <div class="collection-apps layout-playground" id="gamesApps">
                            <!-- Kids apps will be loaded dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Magazines & Newspapers Section -->
                <div class="category-row newspaper-row">
                    <div class="category-collection news-collection newspaper-collection">
                        <div class="collection-header">
                            <h3 class="collection-title">
                                <span class="collection-icon">📰</span>
                                Magazines & Newspapers
                            </h3>
                            <a href="./type.html?category=Magazines%20%26%20Newspapers" class="view-more-link">View All</a>
                        </div>
                        <div class="collection-apps layout-newspaper" id="newsApps">
                            <!-- Magazines & Newspapers apps will be loaded dynamically -->
                        </div>
                    </div>
                </div>

            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="images/logo.png" alt="AppStore Recommendations Logo">
                        <span>AppStore Recommendations</span>
                    </div>
                    <p class="footer-description">
                        Your trusted source for discovering the best apps from the Apple App Store.
                        We curate and recommend apps across all categories to help you find exactly what you need.
                    </p>
                </div>

                <div class="footer-section">
                    <h3 class="footer-title">Quick Links</h3>
                    <ul class="footer-links">
                        <li><a href="./Privacy.html" id="privacyLink">Privacy Policy</a></li>
                        <li><a href="./Terms.html" id="termsLink">Terms of Service</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3 class="footer-title">Hot Categories</h3>
                    <ul class="footer-links" id="hotCategories">
                        <!-- Hot categories will be loaded dynamically -->
                    </ul>
                </div>

            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 AppStore Recommendations. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="18,15 12,9 6,15"></polyline>
        </svg>
    </button>

    <!-- Scripts -->
    <script src="js/common.js"></script>
    <script src="js/index.js"></script>
    <script src="./js/hero-animations.js"></script>
</body>
</html>
