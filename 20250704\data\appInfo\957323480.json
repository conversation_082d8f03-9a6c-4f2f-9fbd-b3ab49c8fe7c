{"isGameCenterEnabled": false, "features": ["iosUniversal"], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "Watch4-Watch4", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": [], "kind": "software", "artistViewUrl": "https://apps.apple.com/us/developer/beijing-xia<PERSON>-co-ltd/id377175754?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/3d/e9/c9/3de9c977-7ec6-0d16-df96-56ebb5562eef/AppIcon-0-1x_U007emarketing-0-8-0-0-sRGB-0-85-220-0.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/3d/e9/c9/3de9c977-7ec6-0d16-df96-56ebb5562eef/AppIcon-0-1x_U007emarketing-0-8-0-0-sRGB-0-85-220-0.png/100x100bb.jpg", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/8b/05/b6/8b05b612-a240-8265-0de3-fa38d15a9e7e/bfb23b53-b411-4a9c-bcd3-ebb82ce7289a_full.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/9e/9e/d2/9e9ed2e2-a0ee-4085-3913-f2bf63551a13/d53ff325-0921-4804-844a-ebd9e38b70ad_1.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/1a/7e/3a/1a7e3ac4-291c-7994-9c15-82d6dd463d6b/914e1dda-cc41-45aa-a00f-d9a8c06c5708_2.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/5b/e0/60/5be060a7-4c9a-6508-f4ad-ee9471885c33/1dcda7b0-87e9-45eb-84fb-0b11faaeeac2_3.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/82/69/76/826976d8-45f9-3678-3a4b-838023785f7e/b7c52882-8d5f-46df-aecf-7b6a632c5c00_4.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/b2/c6/ed/b2c6ed5f-4649-7552-bda7-58e873d634e3/772e35d8-d33a-4812-963a-3daf85294c38_5.png/392x696bb.png"], "ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/50/e0/a8/50e0a8a9-6a5d-b900-212d-3d4c308ea272/3d5b0807-4dc5-4762-848f-546aa2fa1e67__U5168_U5bb6_U798f.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/ad/8f/3c/ad8f3cd7-5e5f-a82b-4cba-1589c1faab16/7fa194aa-4fcc-4ddb-ae55-b57361bde0a5_1.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/37/25/64/372564e9-06a2-c35a-eef2-44064d254cfb/0652a1ae-c6a1-4ed6-87fb-8d58b3af4156_2.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/ad/f1/56/adf15643-1615-6b85-f56d-104bf87ef734/a449929a-c889-456d-b23a-22e8f3cf3a9c_3.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/b7/8a/e6/b78ae6e0-bf1a-3000-cbb8-3a79fd0fc2b6/11ac717f-4d34-4261-b5a5-426c9e50e186_4.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/9d/e7/95/9de795cd-5d81-7edf-a3d4-9cd2df0ced86/053572ec-e6b1-49a7-b06c-1003ed5d213d_5.png/576x768bb.png"], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/3d/e9/c9/3de9c977-7ec6-0d16-df96-56ebb5562eef/AppIcon-0-1x_U007emarketing-0-8-0-0-sRGB-0-85-220-0.png/512x512bb.jpg", "artistId": 377175754, "artistName": "Beijing Xiaomi Co., Ltd", "genres": ["Lifestyle", "Utilities"], "price": 0, "releaseDate": "2015-01-27T18:32:39Z", "trackId": 957323480, "trackName": "Xiaomi Home", "primaryGenreName": "Lifestyle", "primaryGenreId": 6012, "bundleId": "com.xiaomi.mihome", "genreIds": ["6012", "6002"], "isVppDeviceBasedLicensingEnabled": true, "sellerName": "Beijing Xiaomi Co., Ltd", "currentVersionReleaseDate": "2025-06-13T23:42:04Z", "releaseNotes": "Fixed:\n• Fixed some bugs", "version": "10.7.201", "wrapperType": "software", "currency": "USD", "description": "A professional app to help you manage your intelligent devices. \n•  Add new devices with a few easy steps\n•  Control your device wherever you are \n•  Get the status of you devices in real time\n•  Share your devices with friends and family members\n•  Set up and perform intelligent tasks\n•  Continued use of GPS running in the background can dramatically decrease battery life\n\n【Mi Cloud Storage Automatic Renewal Service Description 】\n- Renewal Service:7-day overwriting Renewal storage plan,30-day overwriting Renewal storage plan\n- Renewal period:one month\n- Renewal price: 7-day overwriting Renewal storage plan for 10 yuan/month, 30-day overwriting Renewal storage plan for 24 yuan/month\n- Payment: after confirming the subscription, your Apple ID account will be debited;\n- Cancel renewal:If you cancel the renewal, please manually turn off the automatic renewal function in the iTunes/AppleID settings management 24 hours before the current subscription period expires. Specific path:Please open the Apple mobile phone\"settings\"-->enter\"iTunes store and AppStore\"-->click\"Apple ID\",select\"View Apple ID\",enter the \"Account Settings\"page,click\"subscribe\",select Mi Cloud Storage unsubscribe. If you cancel within 24 hours before the expiration, the subscription fee will be charged;\n- Renewal: Apple ID account will be deducted within 24 hours before expiration. After the deduction is successful, the subscription period will be extended by one subscription period;\n- Mi Cloud Storage Automatic Renewal Service Agreement: https://camera.api.io.mi.com/cloud-service/app/doc/auto_renew-en.html，\n- Mi Cloud Storage User Agreement: https://camera.api.io.mi.com/cloud-service/app/doc/user_licence-en.html\n- About Cloud Storage Automatic Renewal: https://camera.api.io.mi.com/cloud-service/app/my_cloud.html?channel=iosinfo&locale=en#/renew\n- Terms of Use and User Agreement: https://g.home.mi.com/views/user-terms.html?locale=en&type=userLicense\n- Privacy Policy: https://g.home.mi.com/views/user-terms.html?locale=en&type=userPrivacy\n\n【HealthKit】\n- Your record after each weighing: BMI (body mass index), body fat percentage, weight, lean body mass will be shared to Apple Health.", "minimumOsVersion": "11.0", "averageUserRating": 3.67479, "trackCensoredName": "Xiaomi Home", "trackViewUrl": "https://apps.apple.com/us/app/xiaomi-home/id957323480?uo=4", "contentAdvisoryRating": "4+", "languageCodesISO2A": ["AR", "CS", "NL", "EN", "FI", "FR", "DE", "EL", "HE", "HU", "ID", "IT", "JA", "KO", "NB", "PL", "PT", "RO", "RU", "ZH", "SK", "ES", "SV", "TH", "ZH", "TR", "UK", "VI"], "fileSizeBytes": "626666496", "formattedPrice": "Free", "userRatingCountForCurrentVersion": 8779, "trackContentRating": "4+", "sellerUrl": "http://mi.com", "averageUserRatingForCurrentVersion": 3.67479, "userRatingCount": 8779}