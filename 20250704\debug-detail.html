<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Detail Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Detail Page Debug</h1>
    
    <div class="debug-section">
        <h2>1. URL Parameters</h2>
        <div id="urlParams"></div>
    </div>
    
    <div class="debug-section">
        <h2>2. Data Loading Test</h2>
        <div id="dataLoadingStatus">Testing...</div>
        <div id="dataDetails"></div>
    </div>
    
    <div class="debug-section">
        <h2>3. App Search Test</h2>
        <div id="appSearchStatus">Testing...</div>
        <div id="appDetails"></div>
    </div>
    
    <div class="debug-section">
        <h2>4. DOM Elements Test</h2>
        <div id="domElementsStatus">Testing...</div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // Get URL parameters
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }
        
        // Debug URL parameters
        const appIdParam = getUrlParameter('appId');
        const currentAppId = appIdParam ? parseInt(appIdParam, 10) : null;
        
        document.getElementById('urlParams').innerHTML = `
            <p><strong>appId parameter:</strong> ${appIdParam}</p>
            <p><strong>parsed appId:</strong> ${currentAppId}</p>
            <p><strong>Current URL:</strong> ${window.location.href}</p>
        `;
        
        // Test data loading
        async function testDataLoading() {
            try {
                document.getElementById('dataLoadingStatus').innerHTML = '<span class="warning">Loading data...</span>';
                
                // Test direct data loading
                const response = await fetch('./data/typeList.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                document.getElementById('dataLoadingStatus').innerHTML = '<span class="success">✅ Data loaded successfully</span>';
                document.getElementById('dataDetails').innerHTML = `
                    <p><strong>Total apps:</strong> ${data.rankInfo ? data.rankInfo.length : 'No rankInfo'}</p>
                    <p><strong>Has metadata:</strong> ${data.metadata ? 'Yes' : 'No'}</p>
                    <p><strong>First app:</strong> ${data.rankInfo && data.rankInfo[0] ? data.rankInfo[0].appName : 'None'}</p>
                `;
                
                // Test app search
                if (currentAppId && data.rankInfo) {
                    const foundApp = data.rankInfo.find(app => 
                        app.appId === currentAppId || 
                        app.appId.toString() === currentAppId.toString()
                    );
                    
                    if (foundApp) {
                        document.getElementById('appSearchStatus').innerHTML = '<span class="success">✅ App found in data</span>';
                        document.getElementById('appDetails').innerHTML = `
                            <pre>${JSON.stringify(foundApp, null, 2)}</pre>
                        `;
                    } else {
                        document.getElementById('appSearchStatus').innerHTML = '<span class="error">❌ App not found in data</span>';
                        
                        // Show first few apps for comparison
                        const firstFewApps = data.rankInfo.slice(0, 5).map(app => ({
                            appId: app.appId,
                            appName: app.appName
                        }));
                        document.getElementById('appDetails').innerHTML = `
                            <p>Looking for appId: ${currentAppId}</p>
                            <p>First 5 apps in data:</p>
                            <pre>${JSON.stringify(firstFewApps, null, 2)}</pre>
                        `;
                    }
                } else {
                    document.getElementById('appSearchStatus').innerHTML = '<span class="warning">⚠️ No appId to search for</span>';
                }
                
            } catch (error) {
                document.getElementById('dataLoadingStatus').innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
                console.error('Data loading error:', error);
            }
        }
        
        // Test DOM elements
        function testDOMElements() {
            const elements = [
                'appIcon', 'appName', 'appSubtitle', 'appPublisher', 
                'appStars', 'appRatingText', 'appCategory', 'appDescription',
                'appDeveloper', 'appCategoryInfo', 'appVersion', 'relatedApps'
            ];
            
            const results = elements.map(id => {
                const element = document.getElementById(id);
                return `<li><strong>${id}:</strong> ${element ? '✅ Found' : '❌ Missing'}</li>`;
            });
            
            document.getElementById('domElementsStatus').innerHTML = `
                <ul>${results.join('')}</ul>
            `;
        }
        
        // Run tests
        testDataLoading();
        testDOMElements();
    </script>
</body>
</html>
