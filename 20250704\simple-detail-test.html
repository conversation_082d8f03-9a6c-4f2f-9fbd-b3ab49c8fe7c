<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Detail Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
        .warning { background: #fff3cd; }
    </style>
</head>
<body>
    <h1>Simple Detail Test</h1>
    <div id="results"></div>

    <script>
        async function testDetailPage() {
            const results = [];
            const currentAppId = 309735670;
            
            try {
                // Test basic data loading
                results.push('Loading basic data...');
                const response = await fetch('./data/typeList.json');
                const appData = await response.json();
                
                const basicAppInfo = appData.rankInfo.find(app => 
                    app.appId === currentAppId || 
                    app.appId.toString() === currentAppId.toString()
                );
                
                if (basicAppInfo) {
                    results.push(`✅ Found basic app: ${basicAppInfo.appName}`);
                    results.push(`   Publisher: ${basicAppInfo.publisher}`);
                    results.push(`   Category: ${basicAppInfo.type}`);
                    results.push(`   Subtitle: ${basicAppInfo.subtitle || 'None'}`);
                } else {
                    results.push('❌ Basic app not found');
                    return;
                }
                
                // Test detailed data loading
                results.push('Loading detailed data...');
                let detailedAppInfo = null;
                try {
                    const detailedResponse = await fetch(`./data/appInfo/${currentAppId}.json`);
                    if (detailedResponse.ok) {
                        detailedAppInfo = await detailedResponse.json();
                        results.push(`✅ Found detailed app info`);
                        results.push(`   Description length: ${detailedAppInfo.description ? detailedAppInfo.description.length : 0}`);
                        results.push(`   Screenshots: ${detailedAppInfo.screenshotUrls ? detailedAppInfo.screenshotUrls.length : 0}`);
                        
                        if (detailedAppInfo.description) {
                            results.push(`   Description preview: ${detailedAppInfo.description.substring(0, 100)}...`);
                        }
                    } else {
                        results.push('⚠️ Detailed app info not found (404)');
                    }
                } catch (error) {
                    results.push(`⚠️ Error loading detailed info: ${error.message}`);
                }
                
                // Test merged data
                const fullAppInfo = { ...(basicAppInfo || {}), ...(detailedAppInfo || {}) };
                const description = fullAppInfo.description || fullAppInfo.subtitle || 'No description available for this app.';
                
                results.push('Testing final description:');
                results.push(`   Final description length: ${description.length}`);
                results.push(`   Final description preview: ${description.substring(0, 200)}...`);
                
                // Test related apps
                const category = basicAppInfo.type;
                const relatedApps = appData.rankInfo
                    .filter(app => app.type === category && app.appId !== currentAppId)
                    .sort((a, b) => b.rating - a.rating)
                    .slice(0, 6);
                
                results.push(`✅ Found ${relatedApps.length} related apps in "${category}" category`);
                relatedApps.forEach((app, index) => {
                    results.push(`   ${index + 1}. ${app.appName} (${app.rating})`);
                });
                
            } catch (error) {
                results.push(`❌ Error: ${error.message}`);
                console.error('Test error:', error);
            }
            
            // Display results
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = results.map(result => {
                let className = 'result';
                if (result.includes('✅')) className += ' success';
                else if (result.includes('❌')) className += ' error';
                else if (result.includes('⚠️')) className += ' warning';
                
                return `<div class="${className}">${result}</div>`;
            }).join('');
        }
        
        testDetailPage();
    </script>
</body>
</html>
