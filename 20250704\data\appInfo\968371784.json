{"isGameCenterEnabled": false, "artistViewUrl": "https://apps.apple.com/us/developer/lichess-org/id968371783?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/9c/70/77/9c707798-0009-c45c-f9f4-4b1e98faf9a6/AppIcon-0-1x_U007emarketing-0-7-0-85-220.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/9c/70/77/9c707798-0009-c45c-f9f4-4b1e98faf9a6/AppIcon-0-1x_U007emarketing-0-7-0-85-220.png/100x100bb.jpg", "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "features": ["iosUniversal"], "advisories": [], "kind": "software", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/bf/43/f6/bf43f621-1e3d-292b-fb81-b9dc355bbf05/mzl.ssytoayz.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/3a/0f/aa/3a0faa88-0999-20e6-659f-a1b5908993d7/mzl.wplnndna.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/eb/29/34/eb29343f-84b9-026b-8f4c-23eaaa0e5ce0/mzl.uobfnpnw.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/b7/11/28/b71128e6-2e1a-709f-470e-b6f98615eacf/mzl.rsltaiwg.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/75/ea/bd/75eabd51-162f-5260-916f-97a94cd1557d/mzl.unzunvek.png/392x696bb.png"], "ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/99/1f/c1/991fc11f-c474-4ef6-c896-54362d20b81c/pr_source.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/6d/37/10/6d3710e2-cc15-60f7-5123-8ea4f98e18ea/pr_source.png/552x414bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/a9/8a/52/a98a52fa-ed31-60bb-8c51-367ab2e5637c/pr_source.png/552x414bb.png"], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/9c/70/77/9c707798-0009-c45c-f9f4-4b1e98faf9a6/AppIcon-0-1x_U007emarketing-0-7-0-85-220.png/512x512bb.jpg", "trackCensoredName": "lichess • Online Chess", "trackViewUrl": "https://apps.apple.com/us/app/lichess-online-chess/id968371784?uo=4", "contentAdvisoryRating": "4+", "averageUserRating": 3.85936, "artistId": 968371783, "artistName": "LICHESS.ORG", "genres": ["Entertainment", "Games", "Strategy", "Board"], "price": 0, "bundleId": "org.lichess.mobileapp.official", "trackId": 968371784, "trackName": "lichess • Online Chess", "releaseDate": "2015-03-04T05:10:55Z", "genreIds": ["6016", "6014", "7017", "7004"], "primaryGenreName": "Entertainment", "primaryGenreId": 6016, "isVppDeviceBasedLicensingEnabled": true, "sellerName": "LICHESS.ORG", "currentVersionReleaseDate": "2022-12-06T19:04:23Z", "releaseNotes": "What's changed:\n- Fix timeline XSS by @jas14\n- Disable back button on game while playing by @veloce\n- Update to capacitor 4.0 by @veloce", "version": "8.0.0", "wrapperType": "software", "currency": "USD", "description": "Built for the love of chess, this app is open source and free for all.\n\n- 150 000 individual users daily and growing fast.\n- Play bullet, blitz, classical, and correspondence chess\n- Play in arena tournaments\n- Find, follow, challenge players\n- See your games stats\n- Practice with chess puzzles\n- Many variants, available online and offline: Crazyhouse, Chess 960, King Of The Hill, Three-check, Antichess, Atomic chess, Horde, Racing Kings!\n- Game analysis with local computer evaluation\n- Server computer analysis with move annotations and game summary\n- Opening explorer\n- Play against offline computer\n- Over The Board mode to play offline with a friend\n- Standalone chess clock with multiple time settings\n- Board editor\n- Available in 80 languages\n- Designed for both phones and tablets, supporting landscape mode\n- 100% free, without ads, and opensource!\n \nChess variants are supported! Try them all:\n\n- Crazyhouse: every time a piece is captured the capturing player gets a piece of the same type and of their color in their pocket.\n- Chess960: pieces on the home rank are randomised.\n- King Of The Hill: bring your king to the center to win.\n- Threecheck: check the opponent king three times.\n- Antichess: lose all your pieces to win.\n- Atomic Chess: nuke the opponent.\n- Horde: destroy the horde to win.\n- Racing Kings: Race your king to the eighth rank to win.\n \nJust like http://lichess.org, this application is open source, and respects user freedom. It is entirely free and without ads, now and forever.\n\nLearn more about lichess philosophy: http://lichess.org/blog/U4skkUQAAEAAhIGz/why-is-lichess-free\nSource code of the mobile application: https://github.com/veloce/lichobile\nSource code of the website and server: https://github.com/ornicar/lila\n\nBig thanks to the volunteers behind this app:\n- Vincent Velociter (lead dev) https://github.com/veloce\n- Renaud Bressand (design) https://twitter.com/RenaudBressand\n- Thibault Duplessis (dev) https://github.com/ornicar\n- Sebastien Renault (dev of stockfish plugin) https://github.com/srenault\n- Mark Henle (dev) https://github.com/freefal\n\nCheck out the app page on http://lichess.org/mobile, and follow us on twitter for updates: https://twitter.com/lichessorg", "minimumOsVersion": "13.0", "languageCodesISO2A": ["EN"], "sellerUrl": "http://lichess.org/mobile", "fileSizeBytes": "71818240", "formattedPrice": "Free", "userRatingCountForCurrentVersion": 2062, "trackContentRating": "4+", "averageUserRatingForCurrentVersion": 3.85936, "userRatingCount": 2062}