<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Detail Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; max-height: 200px; }
        .app-info { background: #f9f9f9; padding: 15px; margin: 10px 0; }
        .related-apps { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 16px; margin: 20px 0; }
        .related-app-card { background: #f9f9f9; border: 1px solid #ddd; border-radius: 8px; padding: 16px; cursor: pointer; }
        .related-app-header { display: flex; align-items: center; gap: 12px; margin-bottom: 12px; }
        .related-app-icon { width: 48px; height: 48px; border-radius: 8px; object-fit: cover; }
        .related-app-info { flex: 1; }
        .related-app-name { font-weight: 600; margin-bottom: 4px; }
        .related-app-publisher { font-size: 14px; color: #666; }
        .related-app-rating { display: flex; align-items: center; gap: 8px; font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <h1>🔍 Debug Detail Page - Final Test</h1>
    
    <div class="test-section">
        <h2>1. Basic App Data</h2>
        <div id="basicDataStatus">Loading...</div>
        <div id="basicAppInfo"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Detailed App Data</h2>
        <div id="detailedDataStatus">Loading...</div>
        <div id="detailedAppInfo"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Description Test</h2>
        <div id="descriptionTest"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Screenshots Test</h2>
        <div id="screenshotsTest"></div>
    </div>
    
    <div class="test-section">
        <h2>5. Related Apps Test</h2>
        <div id="relatedAppsStatus">Testing...</div>
        <div id="relatedApps" class="related-apps"></div>
    </div>

    <script>
        const currentAppId = 309735670;
        let basicAppInfo = null;
        let detailedAppInfo = null;
        
        // Helper functions
        function generateStarRating(rating) {
            const stars = Math.round(rating);
            return '★'.repeat(stars) + '☆'.repeat(5 - stars);
        }
        
        function formatDescription(description) {
            if (!description) return 'No description available.';
            return description.replace(/\n/g, '<br>');
        }
        
        function createRelatedAppCard(app) {
            const card = document.createElement('div');
            card.className = 'related-app-card';
            card.addEventListener('click', () => {
                alert(`Would navigate to: detail.html?appId=${app.appId}`);
            });
            
            const starsHTML = generateStarRating(app.rating);
            
            card.innerHTML = `
                <div class="related-app-header">
                    <img src="${app.icon}" alt="${app.appName}" class="related-app-icon">
                    <div class="related-app-info">
                        <h4 class="related-app-name">${app.appName}</h4>
                        <p class="related-app-publisher">${app.publisher}</p>
                    </div>
                </div>
                <div class="related-app-rating">
                    <div class="rating-stars">${starsHTML}</div>
                    <span>${app.rating.toFixed(1)}</span>
                </div>
            `;
            
            return card;
        }
        
        async function testDetailPage() {
            try {
                // Test 1: Load basic app data
                document.getElementById('basicDataStatus').innerHTML = '<span class="warning">Loading basic data...</span>';
                
                const response = await fetch('./data/typeList.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const appData = await response.json();
                basicAppInfo = appData.rankInfo.find(app => 
                    app.appId === currentAppId || 
                    app.appId.toString() === currentAppId.toString()
                );
                
                if (basicAppInfo) {
                    document.getElementById('basicDataStatus').innerHTML = '<span class="success">✅ Basic data loaded</span>';
                    document.getElementById('basicAppInfo').innerHTML = `
                        <div class="app-info">
                            <h3>${basicAppInfo.appName}</h3>
                            <p><strong>Publisher:</strong> ${basicAppInfo.publisher}</p>
                            <p><strong>Category:</strong> ${basicAppInfo.type}</p>
                            <p><strong>Rating:</strong> ${basicAppInfo.rating} (${basicAppInfo.num})</p>
                            <p><strong>Subtitle:</strong> ${basicAppInfo.subtitle || 'None'}</p>
                        </div>
                    `;
                } else {
                    document.getElementById('basicDataStatus').innerHTML = '<span class="error">❌ App not found in basic data</span>';
                }
                
                // Test 2: Load detailed app data
                document.getElementById('detailedDataStatus').innerHTML = '<span class="warning">Loading detailed data...</span>';
                
                try {
                    const detailedResponse = await fetch(`./data/appInfo/${currentAppId}.json`);
                    if (!detailedResponse.ok) {
                        throw new Error(`HTTP error! status: ${detailedResponse.status}`);
                    }
                    
                    detailedAppInfo = await detailedResponse.json();
                    document.getElementById('detailedDataStatus').innerHTML = '<span class="success">✅ Detailed data loaded</span>';
                    
                    document.getElementById('detailedAppInfo').innerHTML = `
                        <div class="app-info">
                            <p><strong>Has Description:</strong> ${detailedAppInfo.description ? 'Yes' : 'No'}</p>
                            <p><strong>Description Length:</strong> ${detailedAppInfo.description ? detailedAppInfo.description.length : 0} chars</p>
                            <p><strong>Screenshots:</strong> ${detailedAppInfo.screenshotUrls ? detailedAppInfo.screenshotUrls.length : 0}</p>
                            <p><strong>Artist Name:</strong> ${detailedAppInfo.artistName || 'None'}</p>
                        </div>
                    `;
                } catch (error) {
                    document.getElementById('detailedDataStatus').innerHTML = `<span class="error">❌ Failed to load detailed data: ${error.message}</span>`;
                }
                
                // Test 3: Description
                const fullAppInfo = { ...basicAppInfo, ...detailedAppInfo };
                const description = fullAppInfo.description || fullAppInfo.subtitle || 'No description available for this app.';
                
                document.getElementById('descriptionTest').innerHTML = `
                    <div class="app-info">
                        <h4>Final Description:</h4>
                        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                            ${formatDescription(description)}
                        </div>
                    </div>
                `;
                
                // Test 4: Screenshots
                if (detailedAppInfo && detailedAppInfo.screenshotUrls && detailedAppInfo.screenshotUrls.length > 0) {
                    const screenshotsHtml = detailedAppInfo.screenshotUrls.slice(0, 3).map(url => 
                        `<img src="${url}" alt="Screenshot" style="width: 100px; height: auto; margin: 5px; border: 1px solid #ddd;">`
                    ).join('');
                    
                    document.getElementById('screenshotsTest').innerHTML = `
                        <div class="app-info">
                            <p><strong>Found ${detailedAppInfo.screenshotUrls.length} screenshots:</strong></p>
                            ${screenshotsHtml}
                        </div>
                    `;
                } else {
                    document.getElementById('screenshotsTest').innerHTML = '<div class="app-info">No screenshots available</div>';
                }
                
                // Test 5: Related Apps
                if (basicAppInfo) {
                    const category = basicAppInfo.type;
                    const relatedApps = appData.rankInfo
                        .filter(app => app.type === category && app.appId !== currentAppId)
                        .sort((a, b) => b.rating - a.rating)
                        .slice(0, 6);
                    
                    document.getElementById('relatedAppsStatus').innerHTML = 
                        `<span class="success">✅ Found ${relatedApps.length} related apps in "${category}" category</span>`;
                    
                    const relatedAppsContainer = document.getElementById('relatedApps');
                    relatedAppsContainer.innerHTML = '';
                    
                    relatedApps.forEach(app => {
                        const card = createRelatedAppCard(app);
                        relatedAppsContainer.appendChild(card);
                    });
                }
                
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('basicDataStatus').innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
            }
        }
        
        // Run test
        testDetailPage();
    </script>
</body>
</html>
