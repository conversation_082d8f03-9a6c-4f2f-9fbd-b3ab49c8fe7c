/**
 * App detail page specific JavaScript functionality
 */

// Page-specific variables
let currentAppId = '';
let appDetails = null;
let basicAppInfo = null;
let screenshots = [];
let currentScreenshotIndex = 0;

/**
 * Initialize detail page
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeDetailPage();
});

/**
 * Initialize detail page functionality
 */
async function initializeDetailPage() {
    try {
        // Get app ID from URL parameter
        const appIdParam = getUrlParameter('appId');
        currentAppId = appIdParam ? parseInt(appIdParam, 10) : null;

        if (!currentAppId) {
            showError('No app ID specified. Redirecting to homepage...');
            setTimeout(() => {
                window.location.href = './index.html';
            }, 2000);
            return;
        }

        // Wait for common data to load
        if (!appData) {
            appData = await loadAppData();
        }

        // Get basic app info from the main data
        if (appData && appData.rankInfo) {
            basicAppInfo = appData.rankInfo.find(app => app.appId === currentAppId);
        }
        
        // Load detailed app information (try to load from individual file)
        try {
            appDetails = await loadData(`./data/appInfo/${currentAppId}.json`);
        } catch (error) {
            console.log('Individual app file not found, using basic info only');
            appDetails = null;
        }

        // Always populate details if we have basic info (use detailed info when available)
        if (basicAppInfo || appDetails) {
            populateAppDetails();
            initializeScreenshots();
            initializeActions();
            loadRelatedApps();

            // Initialize categories dropdown for header navigation
            if (appData && appData.typeList) {
                initializeCategoriesDropdown(appData.typeList);
            }

            // Initialize lazy loading after content is loaded
            setTimeout(() => {
                initGlobalLazyLoading();
            }, 100);
        } else {
            showAppNotFound();
        }
    } catch (error) {
        console.error('Error initializing detail page:', error);
    }
}

/**
 * Populate app details on the page
 */
function populateAppDetails() {
    // Use basic info as fallback for missing detailed info
    const appInfo = { ...basicAppInfo, ...appDetails };
    
    // Update page title
    document.title = `${appInfo.appName || 'App'} - AppStore Recommendations`;
    
    // Update app header - use artworkUrl512 for higher quality icon
    const iconUrl = appInfo.artworkUrl512 || appInfo.icon || '';
    updateElement('appIcon', 'src', iconUrl);
    updateElement('appIcon', 'alt', `${appInfo.appName} Icon`);
    updateElement('appName', 'textContent', appInfo.appName || 'Unknown App');
    updateElement('appSubtitle', 'textContent', appInfo.subtitle || '');
    updateElement('appPublisher', 'textContent', appInfo.publisher || appInfo.artistName || 'Unknown Publisher');
    
    // Update rating
    if (appInfo.rating) {
        const starsHTML = generateStarRating(appInfo.rating);
        updateElement('appStars', 'innerHTML', starsHTML);
        updateElement('appRatingText', 'textContent', `${appInfo.rating.toFixed(1)} (${formatNumber(appInfo.num || 0)})`);
    }
    
    // Update category
    updateElement('appCategory', 'textContent', appInfo.type || appInfo.primaryGenreName || 'Unknown');
    
    // Update description
    const description = appInfo.description || appInfo.subtitle || 'No description available for this app.';
    updateElement('appDescription', 'innerHTML', formatDescription(description));
    
    // Update app information
    updateAppInformation(appInfo);
}

/**
 * Update app information section
 */
function updateAppInformation(appInfo) {
    updateElement('appDeveloper', 'textContent', appInfo.publisher || appInfo.artistName || 'Unknown');
    updateElement('appCategoryInfo', 'textContent', appInfo.type || appInfo.primaryGenreName || 'Unknown');
    updateElement('appVersion', 'textContent', appInfo.version || 'Unknown');
    updateElement('appSize', 'textContent', formatFileSize(appInfo.fileSizeBytes));
    updateElement('appCompatibility', 'textContent', getCompatibilityInfo(appInfo));
    updateElement('appLastUpdated', 'textContent', formatDate(appInfo.lastReleaseTime || appInfo.currentVersionReleaseDate));
}

/**
 * Initialize screenshots functionality
 */
function initializeScreenshots() {
    if (appDetails && appDetails.screenshotUrls) {
        screenshots = appDetails.screenshotUrls;
        renderScreenshots();
        initializeScreenshotModal();
    } else {
        // Hide screenshots section if no screenshots available
        const screenshotsSection = document.querySelector('.screenshots-section');
        if (screenshotsSection) {
            screenshotsSection.style.display = 'none';
        }
    }
}

/**
 * Render screenshots
 */
function renderScreenshots() {
    const screenshotsList = document.getElementById('screenshotsList');
    
    if (!screenshotsList || screenshots.length === 0) {
        return;
    }
    
    screenshotsList.innerHTML = '';
    
    screenshots.forEach((screenshot, index) => {
        const screenshotItem = document.createElement('div');
        screenshotItem.className = 'screenshot-item';
        // screenshotItem.addEventListener('click', () => openScreenshotModal(index));
        
        const img = document.createElement('img');
        img.src = './images/lazy.gif';
        img.dataset.src = screenshot;
        img.alt = `Screenshot ${index + 1}`;
        img.className = 'lazy-load';
        
        screenshotItem.appendChild(img);
        screenshotsList.appendChild(screenshotItem);
    });
}

/**
 * Initialize screenshot modal
 */
function initializeScreenshotModal() {
    const modal = document.getElementById('screenshotModal');
    const closeBtn = document.getElementById('modalClose');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    if (closeBtn) {
        closeBtn.addEventListener('click', closeScreenshotModal);
    }
    
    if (prevBtn) {
        prevBtn.addEventListener('click', showPreviousScreenshot);
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', showNextScreenshot);
    }
    
    // Close modal when clicking outside
    if (modal) {
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                closeScreenshotModal();
            }
        });
    }
    
    // Keyboard navigation
    document.addEventListener('keydown', function(event) {
        const modal = document.getElementById('screenshotModal');
        if (modal && modal.classList.contains('visible')) {
            switch (event.key) {
                case 'Escape':
                    closeScreenshotModal();
                    break;
                case 'ArrowLeft':
                    showPreviousScreenshot();
                    break;
                case 'ArrowRight':
                    showNextScreenshot();
                    break;
            }
        }
    });
}

/**
 * Open screenshot modal
 */
function openScreenshotModal(index) {
    currentScreenshotIndex = index;
    const modal = document.getElementById('screenshotModal');
    const modalImage = document.getElementById('modalImage');
    
    if (modal && modalImage && screenshots[index]) {
        modalImage.src = screenshots[index];
        modalImage.alt = `Screenshot ${index + 1}`;
        modal.classList.add('visible');
        document.body.style.overflow = 'hidden';
    }
}

/**
 * Close screenshot modal
 */
function closeScreenshotModal() {
    const modal = document.getElementById('screenshotModal');
    
    if (modal) {
        modal.classList.remove('visible');
        document.body.style.overflow = '';
    }
}

/**
 * Show previous screenshot
 */
function showPreviousScreenshot() {
    if (screenshots.length > 0) {
        currentScreenshotIndex = (currentScreenshotIndex - 1 + screenshots.length) % screenshots.length;
        const modalImage = document.getElementById('modalImage');
        
        if (modalImage) {
            modalImage.src = screenshots[currentScreenshotIndex];
            modalImage.alt = `Screenshot ${currentScreenshotIndex + 1}`;
        }
    }
}

/**
 * Show next screenshot
 */
function showNextScreenshot() {
    if (screenshots.length > 0) {
        currentScreenshotIndex = (currentScreenshotIndex + 1) % screenshots.length;
        const modalImage = document.getElementById('modalImage');
        
        if (modalImage) {
            modalImage.src = screenshots[currentScreenshotIndex];
            modalImage.alt = `Screenshot ${currentScreenshotIndex + 1}`;
        }
    }
}

/**
 * Initialize action buttons
 */
function initializeActions() {
    const downloadBtn = document.getElementById('downloadBtn');
    const shareBtn = document.getElementById('shareBtn');
    
    if (downloadBtn && appDetails) {
        downloadBtn.addEventListener('click', function() {
            // Open App Store link
            const appStoreUrl = appDetails.trackViewUrl || `https://apps.apple.com/app/id${currentAppId}`;
            window.open(appStoreUrl, '_blank');
        });
    }
    
    if (shareBtn) {
        shareBtn.addEventListener('click', shareApp);
    }
}

/**
 * Share app functionality
 */
function shareApp() {
    const appName = basicAppInfo?.appName || 'this app';
    const shareData = {
        title: `Check out ${appName}`,
        text: `I found this amazing app: ${appName}`,
        url: window.location.href
    };
    
    if (navigator.share) {
        navigator.share(shareData).catch(console.error);
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            showSuccess('App link copied to clipboard!');
        }).catch(() => {
            showError('Failed to copy link. Please copy the URL manually.');
        });
    }
}

/**
 * Load related apps
 */
function loadRelatedApps() {
    if (!basicAppInfo || !appData || !appData.rankInfo) {
        return;
    }

    const category = basicAppInfo.type;
    const relatedApps = appData.rankInfo
        .filter(app => app.type === category && app.appId !== currentAppId)
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 8);

    renderRelatedApps(relatedApps);
}

/**
 * Render related apps
 */
function renderRelatedApps(apps) {
    const relatedAppsContainer = document.getElementById('relatedApps');
    
    if (!relatedAppsContainer) {
        return;
    }
    
    if (apps.length === 0) {
        const relatedSection = document.querySelector('.related-section');
        if (relatedSection) {
            relatedSection.style.display = 'none';
        }
        return;
    }
    
    relatedAppsContainer.innerHTML = '';
    
    apps.forEach(app => {
        const relatedCard = createRelatedAppCard(app);
        relatedAppsContainer.appendChild(relatedCard);
    });
}

/**
 * Create related app card
 */
function createRelatedAppCard(app) {
    const card = document.createElement('div');
    card.className = 'related-app-card';
    card.addEventListener('click', () => {
        window.location.href = `detail.html?appId=${app.appId}`;
    });
    
    const starsHTML = generateStarRating(app.rating);
    
    card.innerHTML = `
        <div class="related-app-header">
            <img src="./images/lazy.gif" data-src="${app.icon}" alt="${app.appName}" class="related-app-icon lazy-load">
            <div class="related-app-info">
                <h4 class="related-app-name">${app.appName}</h4>
                <p class="related-app-publisher">${app.publisher}</p>
            </div>
        </div>
        <div class="related-app-rating">
            <div class="rating-stars">${starsHTML}</div>
            <span>${app.rating.toFixed(1)}</span>
        </div>
    `;
    
    return card;
}

/**
 * Show app not found message
 */
function showAppNotFound() {
    document.body.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; text-align: center; padding: 2rem;">
            <div>
                <h1 style="font-size: 2rem; margin-bottom: 1rem;">App Not Found</h1>
                <p style="margin-bottom: 2rem; color: #666;">The app you're looking for could not be found.</p>
                <a href="./index.html" style="background: #007AFF; color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; text-decoration: none;">Back to Home</a>
            </div>
        </div>
    `;
}

/**
 * Utility functions
 */
function updateElement(id, property, value) {
    const element = document.getElementById(id);
    if (element && value !== undefined && value !== null) {
        if (property === 'textContent' && value === '') {
            element.style.display = 'none';
        } else {
            element[property] = value;
        }
    }
}

function formatDescription(description) {
    return description.split('\n').map(paragraph => 
        paragraph.trim() ? `<p>${paragraph.trim()}</p>` : ''
    ).join('');
}

function formatFileSize(bytes) {
    if (!bytes) return 'Unknown';
    
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

function getCompatibilityInfo(appInfo) {
    if (appInfo.supportedDevices) {
        return appInfo.supportedDevices.slice(0, 3).join(', ');
    }
    return 'iPhone, iPad';
}

function formatDate(dateString) {
    if (!dateString) return 'Unknown';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}

function showSuccess(message) {
    // Similar to showError but with success styling
    const successDiv = document.createElement('div');
    successDiv.className = 'success-notification';
    successDiv.innerHTML = `
        <div class="success-content">
            <span class="success-message">${message}</span>
            <button class="success-close">&times;</button>
        </div>
    `;
    
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--success-color);
        color: white;
        padding: 1rem;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        max-width: 400px;
    `;
    
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
        }
    }, 3000);
    
    const closeBtn = successDiv.querySelector('.success-close');
    closeBtn.addEventListener('click', () => {
        if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
        }
    });
}

/**
 * Initialize categories dropdown for header navigation
 */
function initializeCategoriesDropdown(categories) {
    const dropdown = document.getElementById('categoriesDropdown');

    if (!dropdown || !categories) {
        return;
    }

    dropdown.innerHTML = '';

    categories.forEach(category => {
        const li = document.createElement('li');
        const a = document.createElement('a');
        a.href = `./type.html?category=${encodeURIComponent(category.name)}`;
        a.textContent = category.name;
        a.className = 'dropdown-link';
        li.appendChild(a);
        dropdown.appendChild(li);
    });
}
