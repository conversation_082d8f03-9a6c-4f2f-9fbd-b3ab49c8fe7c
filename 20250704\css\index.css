/* Homepage specific styles */

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: var(--spacing-2xl) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin: 0 auto;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
    font-weight: 400;
}

.hero-search {
    max-width: 500px;
    margin: 0 auto;
}

.hero-search .search-form {
    background-color: white;
    border-radius: var(--radius-xl);
    padding: var(--spacing-xs);
    box-shadow: var(--shadow-lg);
}

.hero-search .search-input {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    padding: var(--spacing-md) var(--spacing-lg);
}

.hero-search .search-input::placeholder {
    color: var(--text-secondary);
}

.hero-search .search-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: static;
}

.hero-search .search-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-1px);
}

/* Categories Section */
.categories-section {
    padding: var(--spacing-2xl) 0;
    background-color: var(--background-secondary);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-xl);
    color: var(--text-primary);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.category-card {
    background-color: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.category-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: white;
}

.category-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.category-count {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Category Collections Section */
.category-collections {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-primary) 100%);
}

/* Category Rows */
.category-row {
    display: grid;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.featured-row {
    grid-template-columns: 1fr 1fr;
}

.popular-row {
    grid-template-columns: repeat(3, 1fr);
}

.essential-row {
    grid-template-columns: repeat(4, 1fr);
}

.lifestyle-row {
    grid-template-columns: 1fr 1fr;
}

.utility-row {
    grid-template-columns: repeat(3, 1fr);
}

.creative-row {
    grid-template-columns: 1fr 1fr;
}

.learning-row {
    grid-template-columns: repeat(3, 1fr);
}

.specialized-row {
    grid-template-columns: repeat(5, 1fr);
}

.category-collection {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.category-collection:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.category-collection::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.collection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.collection-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.collection-icon {
    font-size: 1.5em;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.view-more-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    border: 1px solid transparent;
}

.view-more-link:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateX(4px);
}

/* Collection Layout Styles */
.featured-collection {
    padding: var(--spacing-xl);
}

.featured-collection .collection-title {
    font-size: 1.8rem;
}

.featured-collection .collection-icon {
    font-size: 2rem;
}

.popular-collection .collection-title {
    font-size: 1.4rem;
}

.compact-collection {
    padding: var(--spacing-md);
}

.compact-collection .collection-title {
    font-size: 1.1rem;
}

.compact-collection .collection-icon {
    font-size: 1.2rem;
}

.mini-collection {
    padding: var(--spacing-sm);
}

.mini-collection .collection-title {
    font-size: 0.95rem;
}

.mini-collection .collection-icon {
    font-size: 1rem;
}

/* Collection Apps Layout */
.collection-apps {
    display: grid;
    gap: var(--spacing-md);
}

/* Layout-specific grid configurations */
.layout-featured {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.layout-popular {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
}

.layout-compact {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xs);
}

.layout-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xs);
}

.layout-list {
    grid-template-columns: 1fr;
    gap: var(--spacing-xs);
}

.layout-showcase {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
}

.layout-card {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
}

.layout-mini {
    grid-template-columns: 1fr;
    gap: var(--spacing-xs);
}

/* Category-specific styling */
.business-collection {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-left: 4px solid #3b82f6;
}

.business-collection .collection-title {
    color: #1e40af;
}

.business-collection .collection-icon {
    color: #3b82f6;
}

.entertainment-collection {
    background: linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%);
    border-left: 4px solid #a855f7;
}

.entertainment-collection .collection-title {
    color: #7c3aed;
}

.entertainment-collection .collection-icon {
    color: #a855f7;
}

.health-collection {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-left: 4px solid #22c55e;
}

.health-collection .collection-title {
    color: #15803d;
}

.health-collection .collection-icon {
    color: #22c55e;
}

.photo-collection {
    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
    border-left: 4px solid #f97316;
}

.photo-collection .collection-title {
    color: #ea580c;
}

.photo-collection .collection-icon {
    color: #f97316;
}

.games-collection {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    border-left: 4px solid #ef4444;
}

.games-collection .collection-title {
    /* color: #dc2626; */
}

.games-collection .collection-icon {
    color: #ef4444;
}

/* Additional Category Themes */
.productivity-collection {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-left: 4px solid #0ea5e9;
}

.productivity-collection .collection-title {
    color: #0369a1;
}

.productivity-collection .collection-icon {
    color: #0ea5e9;
}

.finance-collection {
    background: linear-gradient(135deg, #f7fee7 0%, #ecfccb 100%);
    border-left: 4px solid #65a30d;
}

.finance-collection .collection-title {
    color: #365314;
}

.finance-collection .collection-icon {
    color: #65a30d;
}

.music-collection {
    background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%);
    border-left: 4px solid #d946ef;
}

.music-collection .collection-title {
    color: #a21caf;
}

.music-collection .collection-icon {
    color: #d946ef;
}

.social-collection {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-left: 4px solid #3b82f6;
}

.social-collection .collection-title {
    color: #1d4ed8;
}

.social-collection .collection-icon {
    color: #3b82f6;
}

.shopping-collection {
    background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
    border-left: 4px solid #f59e0b;
}

.shopping-collection .collection-title {
    color: #d97706;
}

.shopping-collection .collection-icon {
    color: #f59e0b;
}

.lifestyle-collection {
    background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
    border-left: 4px solid #ec4899;
}

.lifestyle-collection .collection-title {
    color: #be185d;
}

.lifestyle-collection .collection-icon {
    color: #ec4899;
}

.food-collection {
    background: linear-gradient(135deg, #fff7ed 0%, #ffedd5 100%);
    border-left: 4px solid #fb923c;
}

.food-collection .collection-title {
    color: #ea580c;
}

.food-collection .collection-icon {
    color: #fb923c;
}

.utilities-collection {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-left: 4px solid #64748b;
}

.utilities-collection .collection-title {
    color: #334155;
}

.utilities-collection .collection-icon {
    color: #64748b;
}

.weather-collection {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-left: 4px solid #0284c7;
}

.weather-collection .collection-title {
    color: #0369a1;
}

.weather-collection .collection-icon {
    color: #0284c7;
}

.navigation-collection {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-left: 4px solid #16a34a;
}

.navigation-collection .collection-title {
    color: #15803d;
}

.navigation-collection .collection-icon {
    color: #16a34a;
}

.design-collection {
    background: linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%);
    border-left: 4px solid #9333ea;
}

.design-collection .collection-title {
    color: #7c3aed;
}

.design-collection .collection-icon {
    color: #9333ea;
}

.dev-collection {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    border-left: 4px solid #6b7280;
}

.dev-collection .collection-title {
    color: #374151;
}

.dev-collection .collection-icon {
    color: #6b7280;
}

.education-collection {
    background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
    border-left: 4px solid #eab308;
}

.education-collection .collection-title {
    /* color: #a16207; */
}

.education-collection .collection-icon {
    color: #eab308;
}

.books-collection {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border-left: 4px solid #f87171;
}

.books-collection .collection-title {
    /* color: #dc2626; */
}

.books-collection .collection-icon {
    color: #f87171;
}

.reference-collection {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-left: 4px solid #06b6d4;
}

.reference-collection .collection-title {
    /* color: #0891b2; */
}

.reference-collection .collection-icon {
    color: #06b6d4;
}

.medical-collection {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    border-left: 4px solid #10b981;
}

.medical-collection .collection-title {
    /* color: #047857; */
}

.medical-collection .collection-icon {
    color: #10b981;
}

.travel-collection {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border-left: 4px solid #f59e0b;
}

.travel-collection .collection-title {
    /* color: #92400e; */
}

.travel-collection .collection-icon {
    color: #f59e0b;
}

.sports-collection {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-left: 4px solid #22c55e;
}

.sports-collection .collection-title {
    /* color: #16a34a; */
}

.sports-collection .collection-icon {
    color: #22c55e;
}

.kids-collection {
    background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%);
    border-left: 4px solid #c084fc;
}

.kids-collection .collection-title {
    color: #9333ea;
}

.kids-collection .collection-icon {
    color: #c084fc;
}

.news-collection {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-left: 4px solid #475569;
}

.news-collection .collection-title {
    /* color: #334155; */
}

.news-collection .collection-icon {
    color: #475569;
}

/* Collection App Cards */
.collection-app-card {
    display: block;
    background: var(--background-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

#booksApps .collection-app-card{
    width: 32%;
}

.collection-app-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

/* Card Layout Variations */
.card-featured {
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
}

.card-featured .app-icon.large {
    width: 80px;
    height: 80px;
}

.card-featured .app-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
    font-style: italic;
}

.card-popular {
    padding: var(--spacing-md);
}

.card-popular .app-icon.medium {
    width: 60px;
    height: 60px;
}

.card-compact {
    padding: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-compact .app-icon.small {
    width: 40px;
    height: 40px;
}

.card-compact .app-info {
    flex: 1;
}

.card-compact .app-name {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.card-grid {
    padding: var(--spacing-sm);
    text-align: center;
}

.card-grid .app-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto var(--spacing-xs);
}

.card-grid .app-name {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.card-grid .stars.small {
    justify-content: center;
    font-size: 0.8rem;
}

.card-list {
    padding: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-list .app-icon.tiny {
    width: 32px;
    height: 32px;
}

.card-list .app-info {
    flex: 1;
}

.card-list .app-name {
    font-size: var(--font-size-sm);
    margin-bottom: 2px;
}

.card-list .app-publisher {
    font-size: var(--font-size-xs);
}

.card-showcase {
    padding: 0;
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
}

.card-showcase .app-showcase {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.card-showcase .app-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.card-showcase .app-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: var(--spacing-sm);
}

.card-showcase .app-name {
    color: white;
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.card-card {
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
}

.card-card .app-card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.card-card .app-icon {
    width: 56px;
    height: 56px;
    margin-bottom: var(--spacing-sm);
}

.card-mini {
    padding: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.card-mini .app-icon.mini {
    width: 28px;
    height: 28px;
}

.card-mini .app-name {
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.collection-app-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.collection-app-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    object-fit: cover;
    flex-shrink: 0;
}

.collection-app-info {
    flex: 1;
    min-width: 0;
}

.collection-app-name {
    font-size: var(--font-size-md);
    font-weight: 600;
    margin: 0 0 var(--spacing-xs) 0;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.collection-app-publisher {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.collection-app-rating {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-xs);
}

.rating-text {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    white-space: nowrap;
}

.no-apps-message {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: var(--spacing-xl);
    margin: 0;
}

/* Responsive Design for Category Collections */
@media (max-width: 768px) {
    .category-collection {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }

    .collection-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .collection-title {
        font-size: var(--font-size-lg);
    }

    .collection-apps {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .collection-app-card {
        padding: var(--spacing-sm);
    }

    .collection-app-icon {
        width: 40px;
        height: 40px;
    }

    .collection-app-name {
        font-size: var(--font-size-sm);
    }

    .collection-app-publisher {
        font-size: var(--font-size-xs);
    }
}

@media (max-width: 480px) {
    .collection-apps {
        grid-template-columns: 1fr;
    }

    .collection-app-rating {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
}

.apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.app-card {
    background-color: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    cursor: pointer;
}

.app-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.app-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.app-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-lg);
    object-fit: cover;
    flex-shrink: 0;
}

.app-info {
    flex: 1;
    min-width: 0;
}

.app-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.app-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.app-publisher {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.app-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

.app-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.star {
    width: 12px;
    height: 12px;
    fill: var(--warning-color);
}

.star.empty {
    fill: var(--text-tertiary);
}

.rating-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-left: var(--spacing-xs);
}

.app-category {
    background-color: var(--background-secondary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin-top: var(--spacing-xl);
}

.load-more-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-2xl);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.load-more-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.load-more-btn:disabled {
    background-color: var(--text-tertiary);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        padding: var(--spacing-xl) 0;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    /* Category Collections Mobile */
    .category-collections {
        padding: var(--spacing-lg) 0;
    }

    /* Mobile: Stack all rows vertically */
    .category-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .category-collection {
        padding: var(--spacing-md);
    }

    .featured-collection {
        padding: var(--spacing-lg);
    }

    .compact-collection,
    .mini-collection {
        padding: var(--spacing-sm);
    }

    .collection-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .collection-title {
        font-size: var(--font-size-lg);
    }

    /* Mobile: Simplify all layouts to single column or simple grid */
    .layout-featured,
    .layout-popular,
    .layout-compact,
    .layout-card {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .layout-grid,
    .layout-showcase {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xs);
    }

    .layout-list,
    .layout-mini {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }

    .category-card {
        padding: var(--spacing-lg);
    }

    .category-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-xl);
    }

    .apps-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .app-card {
        padding: var(--spacing-md);
    }

    .app-header {
        gap: var(--spacing-sm);
    }

    .app-icon {
        width: 56px;
        height: 56px;
    }

    .section-title {
        font-size: var(--font-size-2xl);
    }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
    .featured-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .popular-row {
        grid-template-columns: repeat(2, 1fr);
    }

    .essential-row {
        grid-template-columns: repeat(2, 1fr);
    }

    .specialized-row {
        grid-template-columns: repeat(3, 1fr);
    }

    .layout-featured {
        grid-template-columns: repeat(3, 1fr);
    }

    .layout-popular,
    .layout-card {
        grid-template-columns: repeat(2, 1fr);
    }

    .layout-compact {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero-search .search-input {
        font-size: var(--font-size-base);
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .hero-search .search-btn {
        padding: var(--spacing-sm) var(--spacing-lg);
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .app-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
}
