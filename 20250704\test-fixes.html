<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test All Fixes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .test-link { display: block; margin: 10px 0; padding: 10px; background: #f0f0f0; text-decoration: none; color: #333; }
        .test-link:hover { background: #e0e0e0; }
    </style>
</head>
<body>
    <h1>🔧 App Store Fixes Test Page</h1>
    
    <div class="test-section">
        <h2>✅ Fixed Issues Summary</h2>
        <ul>
            <li><strong>详情页描述问题</strong> - 修复了描述字段映射 (description || subtitle)</li>
            <li><strong>搜索页面Loading</strong> - 添加了 hideLoadingSkeleton() 函数</li>
            <li><strong>分类页面Loading</strong> - 添加了 ITEMS_PER_PAGE 常量</li>
            <li><strong>相关应用卡片</strong> - 确保使用正确的卡片样式</li>
            <li><strong>getAppDetails函数</strong> - 修复了未定义的函数调用</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 Test Links</h2>
        
        <a href="index.html" class="test-link">
            <strong>首页</strong> - 测试主页显示和导航
        </a>
        
        <a href="search.html" class="test-link">
            <strong>搜索页面</strong> - 测试搜索功能和Loading修复
        </a>
        
        <a href="type.html?category=Business" class="test-link">
            <strong>分类页面 (Business)</strong> - 测试分类显示和Loading修复
        </a>
        
        <a href="type.html?category=Games" class="test-link">
            <strong>分类页面 (Games)</strong> - 测试游戏分类
        </a>
        
        <a href="detail.html?appId=309735670" class="test-link">
            <strong>详情页面 (Indeed Job Search)</strong> - 测试详情显示和相关应用
        </a>
        
        <a href="detail.html?appId=1113153706" class="test-link">
            <strong>详情页面 (Microsoft Teams)</strong> - 测试另一个应用详情
        </a>
    </div>
    
    <div class="test-section">
        <h2>🔍 Test Scenarios</h2>
        <ol>
            <li><strong>搜索测试</strong>: 在搜索页面输入 "business" 或 "game"</li>
            <li><strong>分类测试</strong>: 点击分类页面，检查应用列表是否正常显示</li>
            <li><strong>详情测试</strong>: 点击任意应用，检查描述和相关应用是否显示</li>
            <li><strong>Loading测试</strong>: 观察页面加载时是否有持续的loading状态</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>📊 Data Loading Test</h2>
        <div id="dataStatus">Loading...</div>
        <div id="appCount"></div>
        <div id="categoryCount"></div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // Test data loading
        async function testDataLoading() {
            try {
                const data = await loadAppData();
                if (data && data.rankInfo) {
                    document.getElementById('dataStatus').innerHTML = '<span class="success">✅ Data loaded successfully</span>';
                    document.getElementById('appCount').innerHTML = `<strong>Total Apps:</strong> ${data.rankInfo.length}`;
                    
                    // Count categories
                    const categories = [...new Set(data.rankInfo.map(app => app.type))];
                    document.getElementById('categoryCount').innerHTML = `<strong>Categories:</strong> ${categories.length} (${categories.slice(0, 5).join(', ')}...)`;
                } else {
                    document.getElementById('dataStatus').innerHTML = '<span class="error">❌ Data loading failed</span>';
                }
            } catch (error) {
                document.getElementById('dataStatus').innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
            }
        }
        
        // Run test when page loads
        testDataLoading();
    </script>
</body>
</html>
