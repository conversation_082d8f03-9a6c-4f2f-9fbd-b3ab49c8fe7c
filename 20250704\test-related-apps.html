<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Related Apps</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .related-apps { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 16px; margin: 20px 0; }
        .related-app-card { background: #f9f9f9; border: 1px solid #ddd; border-radius: 8px; padding: 16px; cursor: pointer; }
        .related-app-card:hover { background: #f0f0f0; }
        .related-app-header { display: flex; align-items: center; gap: 12px; margin-bottom: 12px; }
        .related-app-icon { width: 48px; height: 48px; border-radius: 8px; object-fit: cover; }
        .related-app-info { flex: 1; }
        .related-app-name { font-weight: 600; margin-bottom: 4px; }
        .related-app-publisher { font-size: 14px; color: #666; }
        .related-app-rating { display: flex; align-items: center; gap: 8px; font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <h1>🧪 Related Apps Test</h1>
    
    <div class="test-section">
        <h2>1. Data Loading Test</h2>
        <div id="dataStatus">Loading...</div>
    </div>
    
    <div class="test-section">
        <h2>2. Related Apps for Indeed Job Search (Business Category)</h2>
        <div id="relatedAppsStatus">Testing...</div>
        <div id="relatedApps" class="related-apps"></div>
    </div>

    <script>
        // Mock functions
        function generateStarRating(rating) {
            const stars = Math.round(rating);
            return '★'.repeat(stars) + '☆'.repeat(5 - stars);
        }
        
        function createRelatedAppCard(app) {
            const card = document.createElement('div');
            card.className = 'related-app-card';
            card.addEventListener('click', () => {
                alert(`Clicked on ${app.appName} (ID: ${app.appId})`);
            });
            
            const starsHTML = generateStarRating(app.rating);
            
            card.innerHTML = `
                <div class="related-app-header">
                    <img src="${app.icon}" alt="${app.appName}" class="related-app-icon">
                    <div class="related-app-info">
                        <h4 class="related-app-name">${app.appName}</h4>
                        <p class="related-app-publisher">${app.publisher}</p>
                    </div>
                </div>
                <div class="related-app-rating">
                    <div class="rating-stars">${starsHTML}</div>
                    <span>${app.rating.toFixed(1)}</span>
                </div>
            `;
            
            return card;
        }
        
        async function testRelatedApps() {
            try {
                document.getElementById('dataStatus').innerHTML = '<span class="warning">Loading data...</span>';
                
                // Load data
                const response = await fetch('./data/typeList.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const appData = await response.json();
                document.getElementById('dataStatus').innerHTML = '<span class="success">✅ Data loaded successfully</span>';
                
                // Find Indeed Job Search
                const currentAppId = 309735670;
                const currentApp = appData.rankInfo.find(app => app.appId === currentAppId);
                
                if (!currentApp) {
                    document.getElementById('relatedAppsStatus').innerHTML = '<span class="error">❌ Current app not found</span>';
                    return;
                }
                
                // Find related apps (same category)
                const category = currentApp.type;
                const relatedApps = appData.rankInfo
                    .filter(app => app.type === category && app.appId !== currentAppId)
                    .sort((a, b) => b.rating - a.rating)
                    .slice(0, 6);
                
                document.getElementById('relatedAppsStatus').innerHTML = 
                    `<span class="success">✅ Found ${relatedApps.length} related apps in "${category}" category</span>`;
                
                // Render related apps
                const relatedAppsContainer = document.getElementById('relatedApps');
                relatedAppsContainer.innerHTML = '';
                
                relatedApps.forEach(app => {
                    const card = createRelatedAppCard(app);
                    relatedAppsContainer.appendChild(card);
                });
                
                console.log('Related apps:', relatedApps);
                
            } catch (error) {
                document.getElementById('dataStatus').innerHTML = `<span class="error">❌ Error: ${error.message}</span>';
                console.error('Error:', error);
            }
        }
        
        // Run test
        testRelatedApps();
    </script>
</body>
</html>
