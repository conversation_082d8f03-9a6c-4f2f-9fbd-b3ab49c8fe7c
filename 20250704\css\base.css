/* 基础布局样式 */
.hero {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    padding: 120px 0 80px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.container {
    position: relative;
    z-index: 2;
    max-width: 1500px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 内容区样式 */
.hero-content {
    max-width: 1100px;
    margin-bottom: 60px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    color: white;
    margin-bottom: 20px;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    max-width: 90%;
}

/* 按钮样式 */
.hero-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
	justify-content: center;
}

.cta-button {
    font-weight: 600;
    padding: 14px 32px;
    border-radius: 30px;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    transform: translateY(0);
    text-decoration: none;
    cursor: pointer;
}

.cta-button.primary {
    background: white;
    color: #8b5cf6;
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

.cta-button.secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-icon {
    transition: transform 0.3s ease;
}

/* 统计卡片样式 */
.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 25px 30px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    transform: translateY(0);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
    display: block;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.rating-stars {
    display: flex;
    justify-content: center;
    gap: 2px;
    margin-bottom: 8px;
}

.star-icon {
    width: 16px;
    height: 16px;
}

/* 装饰元素 */
.hero::after,
.hero::before {
    content: "";
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
    z-index: 1;
}

.hero::after {
    top: 0;
    right: 0;
    width: 400px;
    height: 400px;
    background: rgba(255, 255, 255, 0.05);
}

.hero::before {
    bottom: 0;
    left: 0;
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
}

/* 交互状态样式 */
.cta-button:hover,
.stat-item:hover {
    transform: translateY(-5px);
}

.cta-button.primary:hover {
    box-shadow: 0 8px 20px rgba(255, 255, 255, 0.4);
}

.cta-button:hover .btn-icon {
    transform: translateX(5px);
}

/* 响应式适配 */
@media (max-width: 768px) {
    .hero {
        padding: 80px 0 60px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .cta-button {
        padding: 12px 24px;
        font-size: 0.9rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        gap: 20px;
    }
    
    .stat-item {
        padding: 20px 20px;
    }
}


/* 分类区块基础样式 */
.category-row.classroom-row {
    padding: 40px 0;
    position: relative;
}

.category-collection.education-collection.classroom-collection {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* 区块标题样式 */
.collection-header {
    padding: 24px 32px;
    border-bottom: 1px solid #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 50%);
	border-radius: 16px;
}

.collection-title {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
}

.collection-icon {
    background: white;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.view-more-link {
    color: white;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
    transition: opacity 0.2s ease;
}

.view-more-link:hover {
    opacity: 0.8;
}

/* 应用卡片容器样式 */
.collection-apps.layout-classroom {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    padding: 24px 32px;
}

/* 应用卡片主体样式 */
.collection-app-card.card-classroom {
    display: block;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.collection-app-card.card-classroom:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

/* 应用图标区域 */
.collection-app-header {
    display: flex;
    padding: 20px;
    gap: 16px;
    border-bottom: 1px solid #f5f5f5;
}

.collection-app-icon {
    width: 64px;
    height: 64px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.collection-app-card.card-classroom:hover .collection-app-icon {
    transform: scale(1.05);
}

.collection-app-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.collection-app-name {
    font-size: 1rem;
    font-weight: 600;
    color: #111;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.collection-app-publisher {
    font-size: 0.85rem;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 评分区域样式 */
.collection-app-rating {
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.star {
    width: 18px;
    height: 18px;
    fill: #FFD700;
    transition: transform 0.2s ease;
}

.collection-app-card.card-classroom:hover .star {
    transform: scale(1.1);
}

.rating-text {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .collection-header {
        padding: 20px;
    }
    
    .collection-title {
        font-size: 1.25rem;
    }
    
    .collection-apps.layout-classroom {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 16px;
        padding: 16px 20px;
    }
    
    .collection-app-header {
        padding: 16px;
        gap: 12px;
    }
    
    .collection-app-icon {
        width: 56px;
        height: 56px;
    }
}

@media (max-width: 480px) {
    .collection-apps.layout-classroom {
        grid-template-columns: 1fr;
    }
    
    .collection-title {
        font-size: 1.1rem;
    }
    
    .collection-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }
}

/* 动态装饰元素 */
.collection-app-card.card-classroom::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 70%, rgba(99, 102, 241, 0.05));
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.collection-app-card.card-classroom:hover::before {
    opacity: 1;
}