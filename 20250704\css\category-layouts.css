/* Category Layout Styles - 24 Unique Layouts */

/* Lazy Loading Styles */
.lazy-load {
    opacity: 1;
    transition: opacity 0.3s ease;
}

.lazy-loaded {
    opacity: 1;
}

/* Base Layout Styles */
.category-row {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg) 0;
}

.collection-apps {
    display: grid;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

/* 1. <PERSON> Grid Layout */
.layout-hero-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
}

.hero-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: var(--spacing-lg);
    color: white;
    position: relative;
    overflow: hidden;
    min-height: 200px;
}

.hero-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    margin-bottom: var(--spacing-md);
}

.hero-info h4 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.hero-rating {
    margin-top: var(--spacing-sm);
}

/* 2. Magazine Layout */
.layout-magazine {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.magazine-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    display: flex;
    min-height: 120px;
}

.magazine-icon {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    margin: var(--spacing-md);
}

.magazine-content {
    padding: var(--spacing-md);
    flex: 1;
}

.magazine-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.app-excerpt {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* 3. Masonry Layout */
.layout-masonry {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.masonry-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    min-height: 150px;
}

.masonry-icon {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.masonry-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: var(--spacing-md);
}

/* 4. Gallery Layout */
.layout-gallery {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
}

.gallery-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.gallery-image {
    aspect-ratio: 1;
    overflow: hidden;
}

.gallery-icon {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-info {
    padding: var(--spacing-sm);
    text-align: center;
}

.gallery-info h4 {
    font-size: 0.9rem;
    font-weight: 600;
}

.gallery-info p {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* 5. Timeline Layout */
.layout-timeline {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    position: relative;
}

.layout-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--primary-color);
}

.timeline-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    background: white;
    padding: var(--spacing-md);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    position: relative;
}

.timeline-marker {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--primary-color);
    position: absolute;
    left: -36px;
    z-index: 2;
}

.timeline-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
}

.timeline-content h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

/* 6. Dashboard Layout */
.layout-dashboard {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    padding: var(--spacing-md);
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    border-left: 4px solid var(--primary-color);
}

.dashboard-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.dashboard-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

.dashboard-header h4 {
    font-size: 1rem;
    font-weight: 600;
}

.rating-badge {
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* 7. Playlist Layout */
.layout-playlist {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.playlist-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    background: white;
    padding: var(--spacing-md);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.playlist-number {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    min-width: 30px;
}

.playlist-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
}

.playlist-info h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

/* 8. Feed Layout */
.layout-feed {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.feed-card {
    background: white;
    border-radius: 12px;
    padding: var(--spacing-md);
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.feed-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.feed-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.feed-meta h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

/* 9. Catalog Layout */
.layout-catalog {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
}

.catalog-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    position: relative;
}

.catalog-image {
    position: relative;
    aspect-ratio: 1;
}

.catalog-icon {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.catalog-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #ff4757;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}

.catalog-details {
    padding: var(--spacing-sm);
    text-align: center;
}

.catalog-details h4 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.app-price {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

/* 10. Pinterest Layout */
.layout-pinterest {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.pinterest-card {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    background: white;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.pinterest-icon {
    width: 100%;
    height: auto;
    aspect-ratio: 3/4;
    object-fit: cover;
}

.pinterest-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: var(--spacing-md);
}

.pinterest-overlay h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

/* 11. Recipe Layout */
.layout-recipe {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
}

.recipe-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.recipe-icon {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.recipe-info {
    padding: var(--spacing-md);
}

.recipe-info h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.recipe-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recipe-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* 12. Toolbox Layout */
.layout-toolbox {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.toolbox-card {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    border-radius: 16px;
    padding: var(--spacing-lg);
    text-align: center;
    color: white;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.toolbox-icon-wrapper {
    margin-bottom: var(--spacing-md);
}

.toolbox-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    margin: 0 auto;
}

.toolbox-label h4 {
    font-weight: 600;
    font-size: 1rem;
}

/* 13. Forecast Layout */
.layout-forecast {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.forecast-card {
    background: linear-gradient(135deg, #00cec9, #55a3ff);
    border-radius: 16px;
    padding: var(--spacing-lg);
    color: white;
    text-align: center;
    min-height: 160px;
}

.forecast-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.forecast-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
}

.forecast-temp {
    font-size: 1.5rem;
    font-weight: 700;
}

.forecast-info h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.forecast-desc {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 14. Map Layout */
.layout-map {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.map-card {
    background: white;
    border-radius: 16px;
    padding: var(--spacing-lg);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    position: relative;
    text-align: center;
    min-height: 150px;
}

.map-pin {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.5rem;
}

.map-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    margin: 0 auto var(--spacing-md);
}

.map-info h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.map-distance {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

/* 15. Portfolio Layout */
.layout-portfolio {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
}

.portfolio-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.portfolio-image {
    position: relative;
    aspect-ratio: 4/3;
}

.portfolio-icon {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-card:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-tools {
    color: white;
    font-size: 2rem;
}

.portfolio-caption {
    padding: var(--spacing-sm);
    text-align: center;
}

.portfolio-caption h4 {
    font-weight: 600;
    font-size: 0.9rem;
}

/* 16. Terminal Layout */
.layout-terminal {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.terminal-card {
    background: #2d3748;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
}

.terminal-header {
    background: #4a5568;
    padding: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.terminal-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.terminal-btn.red { background: #fc8181; }
.terminal-btn.yellow { background: #f6e05e; }
.terminal-btn.green { background: #68d391; }

.terminal-body {
    padding: var(--spacing-md);
    color: #68d391;
    font-family: 'Monaco', 'Menlo', monospace;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.terminal-icon {
    width: 40px;
    height: 40px;
    border-radius: 6px;
}

.terminal-text h4 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.terminal-command {
    font-size: 0.8rem;
    color: #68d391;
}

/* 17. Classroom Layout */
.layout-classroom {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
}

.classroom-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 16px;
    padding: var(--spacing-lg);
    color: white;
    text-align: center;
    min-height: 160px;
}

.classroom-board {
    position: relative;
    margin-bottom: var(--spacing-md);
}

.classroom-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    margin: 0 auto;
}

.classroom-grade {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #feca57;
    color: #2d3436;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 700;
}

.classroom-info h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.classroom-subject {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 18. Library Layout */
.layout-library {
    display: flex;
    /* flex-direction: column; */
    flex-wrap: wrap;
    justify-content: space-between;
    gap: var(--spacing-sm);
}

.library-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    background: linear-gradient(90deg, #8b4513, #a0522d);
    border-radius: 12px;
    padding: var(--spacing-md);
    color: white;
    min-height: 80px;
}

.library-spine {
    width: 60px;
    height: 60px;
    background: #654321;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.library-icon {
    width: 40px;
    height: 40px;
    border-radius: 4px;
}

.library-info {
    flex: 1;
}

.library-info h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.library-author {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: var(--spacing-xs);
}

.library-rating {
    font-size: 0.8rem;
}

/* 19. Encyclopedia Layout */
.layout-encyclopedia {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.encyclopedia-card {
    background: white;
    border-radius: 12px;
    padding: var(--spacing-lg);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    text-align: center;
    position: relative;
    min-height: 150px;
}

.encyclopedia-letter {
    position: absolute;
    top: -15px;
    left: -15px;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
}

.encyclopedia-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    margin: 0 auto var(--spacing-md);
}

.encyclopedia-entry h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.encyclopedia-def {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* 20. Clinic Layout */
.layout-clinic {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.clinic-card {
    background: linear-gradient(135deg, #ff7675, #fd79a8);
    border-radius: 16px;
    padding: var(--spacing-lg);
    color: white;
    text-align: center;
    position: relative;
    min-height: 150px;
}

.clinic-cross {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.5rem;
}

.clinic-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    margin: 0 auto var(--spacing-md);
}

.clinic-info h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.clinic-type {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 21. Journey Layout */
.layout-journey {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
}

.journey-card {
    background: linear-gradient(135deg, #00b894, #00cec9);
    border-radius: 16px;
    padding: var(--spacing-lg);
    color: white;
    text-align: center;
    position: relative;
    min-height: 150px;
}

.journey-path {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.5rem;
}

.journey-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    margin: 0 auto var(--spacing-md);
}

.journey-destination h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.journey-distance {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 22. Scoreboard Layout */
.layout-scoreboard {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.scoreboard-card {
    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
    border-radius: 16px;
    padding: var(--spacing-lg);
    color: white;
    text-align: center;
    position: relative;
    min-height: 160px;
}

.scoreboard-score {
    position: absolute;
    top: -15px;
    right: -15px;
    background: #fdcb6e;
    color: #2d3436;
    padding: 8px 12px;
    border-radius: 16px;
    font-weight: 700;
    font-size: 1.1rem;
}

.scoreboard-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    margin: 0 auto var(--spacing-md);
}

.scoreboard-team h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.scoreboard-league {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 23. Playground Layout */
.layout-playground {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.playground-card {
    background: linear-gradient(135deg, #fd79a8, #fdcb6e);
    border-radius: 20px;
    padding: var(--spacing-lg);
    color: white;
    text-align: center;
    position: relative;
    min-height: 160px;
}

.playground-toy {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.5rem;
}

.playground-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    margin: 0 auto var(--spacing-md);
}

.playground-fun h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.playground-age {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 24. Newspaper Layout */
.layout-newspaper {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
}

.newspaper-card {
    background: white;
    border-radius: 8px;
    padding: var(--spacing-lg);
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    border: 2px solid #ddd;
    text-align: center;
    position: relative;
    min-height: 150px;
}

.newspaper-headline {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.5rem;
}

.newspaper-icon {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    margin: 0 auto var(--spacing-md);
}

.newspaper-article h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    font-family: 'Times New Roman', serif;
}

.newspaper-date {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .layout-hero-grid,
    .layout-gallery,
    .layout-dashboard,
    .layout-catalog,
    .layout-recipe,
    .layout-portfolio,
    .layout-classroom,
    .layout-journey,
    .layout-newspaper {
        grid-template-columns: repeat(2, 1fr);
    }

    .layout-magazine {
        grid-template-columns: 1fr;
    }

    .layout-masonry,
    .layout-pinterest,
    .layout-toolbox,
    .layout-forecast,
    .layout-map,
    .layout-terminal,
    .layout-encyclopedia,
    .layout-clinic,
    .layout-scoreboard,
    .layout-playground {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .layout-hero-grid,
    .layout-gallery,
    .layout-dashboard,
    .layout-masonry,
    .layout-catalog,
    .layout-pinterest,
    .layout-recipe,
    .layout-toolbox,
    .layout-forecast,
    .layout-map,
    .layout-portfolio,
    .layout-terminal,
    .layout-classroom,
    .layout-encyclopedia,
    .layout-clinic,
    .layout-journey,
    .layout-scoreboard,
    .layout-playground,
    .layout-newspaper {
        grid-template-columns: 1fr;
    }

    .hero-card,
    .forecast-card,
    .clinic-card,
    .journey-card,
    .scoreboard-card,
    .playground-card {
        min-height: 120px;
    }

    .hero-icon,
    .forecast-icon,
    .clinic-icon,
    .journey-icon,
    .scoreboard-icon,
    .playground-icon {
        width: 50px;
        height: 50px;
    }
}
